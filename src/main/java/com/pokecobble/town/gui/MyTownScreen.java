package com.pokecobble.town.gui;

import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.TownPlayer;
import com.pokecobble.town.TownPlayerRank;
import com.pokecobble.town.access.TownAccessController;
import com.pokecobble.town.election.Election;
import com.pokecobble.town.election.ElectionManager;
import com.pokecobble.town.network.election.ElectionNetworkHandler;
import com.pokecobble.town.claim.ClaimTool;
import com.pokecobble.town.network.town.TownDataSynchronizer;
import com.pokecobble.town.network.town.TownImageSynchronizer;
import com.pokecobble.town.network.town.TownNetworkHandler;
import com.pokecobble.town.sound.SoundUtil;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.ButtonWidget;
import net.minecraft.client.gui.widget.TextFieldWidget;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;
import com.pokecobble.town.gui.IndependentTownImageRenderer;
import com.pokecobble.town.gui.chat.AnimatedChatInputBox;
import com.pokecobble.town.gui.chat.ChatDisplayArea;
import com.pokecobble.town.network.chat.TownChatClientManager;
import com.pokecobble.town.network.chat.TownChatNetworkHandler;
import com.pokecobble.Pokecobbleclaim;


import com.mojang.blaze3d.systems.RenderSystem;


import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.Comparator;
import java.util.List;
import java.util.Random;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Screen for managing the player's town with subcategories.
 */
public class MyTownScreen extends Screen implements TownDataSynchronizer.TownPlayerUpdateCallback, ElectionNetworkHandler.ElectionUpdateCallback, com.pokecobble.ui.UIDataRefreshManager.RefreshableComponent {
    private final Screen parent;

    // Panel dimensions
    private int panelWidth = 400;
    private int panelHeight = 300;

    // UI colors
    private static final int BACKGROUND_COLOR = 0xCC000000; // Background color for masking

    // Glass Effect Color Palette - consistent with subcategory styling
    private static final int GLASS_PANEL_BG = 0xD0101010;      // Main panel background
    private static final int GLASS_HEADER_BG = 0x60404040;     // Header glass background
    private static final int GLASS_SIDEBAR_BG = 0x60303030;    // Sidebar glass background
    private static final int GLASS_CONTENT_BG = 0x30000000;    // Content area background
    private static final int GLASS_CARD_BG = 0x40303030;       // Card backgrounds
    private static final int GLASS_CARD_HOVER = 0x60404040;    // Card hover state

    // Glass effect highlights and shadows
    private static final int GLASS_TOP_HIGHLIGHT = 0x20FFFFFF;    // Top glass highlight
    private static final int GLASS_LEFT_HIGHLIGHT = 0x20FFFFFF;   // Left glass highlight
    private static final int GLASS_BRIGHT_HIGHLIGHT = 0x40FFFFFF; // Brighter highlights
    private static final int GLASS_INNER_HIGHLIGHT = 0x30FFFFFF;  // Inner glass highlights
    private static final int GLASS_SHADOW = 0x40000000;          // Glass shadows
    private static final int GLASS_BOTTOM_SHADOW = 0x20000000;   // Bottom shadows

    // Text colors
    private static final int TEXT_PRIMARY = 0xFFFFFFFF;     // Primary text
    private static final int TEXT_SECONDARY = 0xFFB0B0B0;   // Secondary text
    private static final int TEXT_MUTED = 0xFF808080;       // Muted text

    // Subcategories
    private List<TownSubcategory> subcategories = new ArrayList<>();
    private TownSubcategory selectedSubcategory;

    // Scrolling
    private int playersScrollOffset = 0;
    private int electionScrollOffset = 0;
    private int sidebarScrollOffset = 0;

    // Hover animation for rank names
    private UUID hoveredPlayerUUID = null;
    private float rankNameAnimation = 0.0f;
    private long lastAnimationTime = 0;



    // Simple image handling - no complex tracking or caching
    private long lastTownDataRequestTime = 0;
    private long lastConfigRequestTime = 0;
    private static final long REQUEST_COOLDOWN_MS = 2000; // 2 seconds cooldown



    // Cache for player list to prevent regeneration on every frame
    private static final Random RANDOM = new Random();
    private UUID lastTownId = null;
    private List<TownPlayer> cachedPlayerList = null;

    // Settings zones scrolling
    private int townSettingsScrollOffset = 0;
    private int playerSettingsScrollOffset = 0;

    // Player list sorting and filtering
    private enum SortType { RANK, NAME, NEWEST, OLDEST }
    private enum FilterType { ALL, ONLINE, OFFLINE }
    private SortType currentSortType = SortType.RANK;
    private FilterType currentFilterType = FilterType.ALL;
    private String searchQuery = "";
    private TextFieldWidget searchField;

    // Context menu for player list
    private boolean showContextMenu = false;
    private int contextMenuX = 0;
    private int contextMenuY = 0;
    private TownPlayer contextMenuPlayer = null;

    // Permission notification state
    private String permissionNotificationText = null;
    private int permissionNotificationColor = 0xFFFF5555; // Red color for permission denied
    private long permissionNotificationStartTime = 0;
    private static final int PERMISSION_NOTIFICATION_DURATION = 100; // 5 seconds in ticks

    // Permission retry mechanism for handling data sync delays
    private long lastPermissionRetryTime = 0;
    private int permissionRetryCount = 0;
    private static final int MAX_PERMISSION_RETRIES = 3;
    private static final long PERMISSION_RETRY_DELAY_MS = 1000; // 1 second delay between retries

    // Button animation states
    private float sortButtonAnimation = 0.0f;
    private float filterButtonAnimation = 0.0f;
    private float inviteButtonAnimation = 0.0f;
    private boolean sortButtonHovered = false;
    private boolean filterButtonHovered = false;
    private boolean inviteButtonHovered = false;

    // Claims subcategory button animation states
    private float claimToolButtonAnimation = 0.0f;
    private float viewBoundariesButtonAnimation = 0.0f;
    private float tagSettingsButtonAnimation = 0.0f;
    private boolean claimToolButtonHovered = false;
    private boolean viewBoundariesButtonHovered = false;
    private boolean tagSettingsButtonHovered = false;

    // Claims history scrolling
    private int claimsHistoryScrollOffset = 0;

    // Jobs subcategory button animation states
    private float refreshJobsButtonAnimation = 0.0f;
    private float incomeDisplayButtonAnimation = 0.0f;
    private boolean refreshJobsButtonHovered = false;
    private boolean incomeDisplayButtonHovered = false;

    // Jobs scrolling and expansion states
    private int jobsScrollOffset = 0;
    private final java.util.Set<com.pokecobble.town.TownJob.JobType> expandedJobs = new java.util.HashSet<>();
    private final java.util.Map<com.pokecobble.town.TownJob.JobType, Float> jobExpansionAnimations = new java.util.HashMap<>();

    // Job filtering
    private enum JobFilterType { ALL, UNLOCKED, LOCKED }
    private JobFilterType currentJobFilterType = JobFilterType.ALL;
    private float jobFilterButtonAnimation = 0.0f;
    private boolean jobFilterButtonHovered = false;

    // Claims subcategory "see more" button animation states
    private float seeMoreButtonAnimation = 0.0f;
    private boolean seeMoreButtonHovered = false;

    // Bank subcategory animation states
    private float depositButtonAnimation = 0.0f;
    private float withdrawButtonAnimation = 0.0f;
    private float bankAppButtonAnimation = 0.0f;
    private float timeDropdownButtonAnimation = 0.0f;
    private boolean depositButtonHovered = false;
    private boolean withdrawButtonHovered = false;
    private boolean bankAppButtonHovered = false;
    private boolean timeDropdownButtonHovered = false;

    // Settings subcategory animation states
    private float townSettingsButtonAnimation = 0.0f;
    private float leaveButtonAnimation = 0.0f;
    private boolean townSettingsButtonHovered = false;
    private boolean leaveButtonHovered = false;

    // Chat subcategory components
    private ChatDisplayArea chatDisplayArea;
    private AnimatedChatInputBox chatInputBox;
    private boolean isChatViewerRegistered = false;
    private UUID currentChatTownId = null;
    private int lastMessageCount = 0; // Track message count for auto-scroll

    // Money graph enhanced states
    private enum TimeMode { HOUR, DAY, MONTH, YEAR }
    private TimeMode currentTimeMode = TimeMode.DAY;
    private int graphScrollOffset = 0;
    private boolean isDragging = false;
    private int lastDragX = 0;
    private int maxScrollOffset = 0;



    // Cache for graph data to avoid regenerating every frame
    private MoneyGraphData[] cachedHourlyData = null;
    private MoneyGraphData[] cachedDailyData = null;
    private MoneyGraphData[] cachedMonthlyData = null;
    private MoneyGraphData[] cachedYearlyData = null;

    // Invite button state
    private int[] inviteButtonBounds = null;
    private boolean showInviteScreen = false;

    // Jobs button coordinates for click detection
    private int jobsButtonX;
    private int jobsButtonY;
    private int jobsButtonWidth;
    private int jobsButtonHeight;

    // Status message
    private Text statusText = Text.empty();
    private int statusColor = 0xFFFFFF;

    // Simple image handling - no complex protection needed like TownImageScreen

    // Circle position and size for the change town button
    private int circleX;
    private int circleY;
    private int circleSize;
    private int circleCenterX;
    private int circleCenterY;
    private int circleRadius;

    public MyTownScreen(Screen parent) {
        super(Text.literal("My Town"));
        this.parent = parent;
    }



    /**
     * Checks if the player has permission to access a subcategory.
     *
     * @param subcategoryName The name of the subcategory
     * @param playerTown The player's town
     * @return True if the player has permission, false otherwise
     */
    private boolean hasPermissionForSubcategory(String subcategoryName, Town playerTown) {
        if (client.player == null || playerTown == null) {
            com.pokecobble.Pokecobbleclaim.LOGGER.info("Permission check failed: client.player=" + client.player + ", playerTown=" + playerTown);
            return false;
        }



        boolean hasPermission = TownAccessController.getInstance().hasPermissionForSubcategory(
            subcategoryName, playerTown, client.player.getUuid());

        return hasPermission;
    }

    /**
     * Renders a permission denied message for a subcategory.
     *
     * @param context The draw context
     * @param subcategoryName The name of the subcategory
     * @param contentX The X position of the content area
     * @param contentY The Y position of the content area
     * @param contentWidth The width of the content area
     * @param contentHeight The height of the content area
     */
    private void renderPermissionDeniedMessage(DrawContext context, String subcategoryName, int contentX, int contentY, int contentWidth, int contentHeight) {
        // Draw a semi-transparent background
        context.fillGradient(contentX, contentY, contentX + contentWidth, contentY + contentHeight, 0x80202020, 0x80303030);

        // Calculate center position
        int centerX = contentX + contentWidth / 2;
        int centerY = contentY + contentHeight / 2;

        // Draw lock icon
        String lockIcon = "🔒";
        int lockWidth = this.textRenderer.getWidth(lockIcon);
        context.drawTextWithShadow(this.textRenderer, lockIcon, centerX - lockWidth / 2, centerY - 40, 0xFFFF5555);

        // Draw permission denied message
        Text deniedText = Text.literal("Access Denied").formatted(Formatting.BOLD, Formatting.RED);
        context.drawCenteredTextWithShadow(this.textRenderer, deniedText, centerX, centerY - 20, 0xFFFFFF);

        // Draw explanation
        String permissionCategory = TownAccessController.getInstance().getPermissionCategory(subcategoryName);
        String permissionName = TownAccessController.getInstance().getPermissionName(subcategoryName);

        if (permissionCategory != null && permissionName != null) {
            Text explanationText = Text.literal("You need the permission: " + permissionName)
                    .formatted(Formatting.YELLOW);
            context.drawCenteredTextWithShadow(this.textRenderer, explanationText, centerX, centerY, 0xFFFFFF);
        }

        // Draw instruction
        Text instructionText = Text.literal("Ask the mayor or an admin for access")
                .formatted(Formatting.WHITE);
        context.drawCenteredTextWithShadow(this.textRenderer, instructionText, centerX, centerY + 20, 0xFFFFFF);
    }

    @Override
    protected void init() {
        super.init();

        // Check if player is still in a town - if not, close this screen
        if (client.player != null) {
            Town playerTown = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTown();
            UUID playerTownId = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTownId();

            if (playerTown == null && playerTownId == null) {
                // Player is definitely not in a town, close this screen and return to parent
                this.close();
                return;
            } else if (playerTown == null && playerTownId != null) {
                // Player has a town ID but town data is not cached - request fresh data
                safeRequestTownData();
                // Also request image selection data
                com.pokecobble.town.client.TownImageSelectionClient.requestSelectionData(playerTownId);
                // Continue with initialization - the screen will handle loading states
            }

            // Always request image selection data for the current town
            if (playerTown != null) {
                com.pokecobble.town.client.TownImageSelectionClient.requestSelectionData(playerTown.getId());
            }
        }

        // Calculate panel dimensions based on screen size - make it even wider
        panelWidth = Math.min(width - 20, 1100); // Increased from 1000 to 1100 and reduced side margins
        panelHeight = height - 20;

        // Calculate positions
        int leftX = (width - panelWidth) / 2;
        int topY = 10;

        // Register for town player updates
        TownDataSynchronizer.registerPlayerUpdateCallback(this);

        // Register for election updates
        ElectionNetworkHandler.registerElectionUpdateCallback(this);

        // Register for player data updates to refresh player list (includes rank changes)
        com.pokecobble.town.client.ClientSyncManager.getInstance().addEventListener("player_updated", this::onPlayerDataUpdated);

        // Register for town list updates to refresh when town data changes
        com.pokecobble.town.client.ClientSyncManager.getInstance().addEventListener("town_list_updated", this::onTownListUpdated);

        // Register for town data updates to refresh claims and other town data
        com.pokecobble.town.client.ClientSyncManager.getInstance().addEventListener("town_updated", this::onTownDataUpdated);

        // Register for chunk data updates to refresh claims data
        com.pokecobble.town.client.ClientSyncManager.getInstance().addEventListener("chunk_updated", this::onChunkDataUpdated);

        // Register this screen with UIDataRefreshManager for automatic refresh handling
        com.pokecobble.ui.UIDataRefreshManager.getInstance().onScreenOpened(this);

        // Only request fresh town data if we don't have any cached data
        // This prevents overwriting recently synchronized claim history
        if (client.player != null) {
            Town playerTown = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTown();
            if (playerTown == null) {
                // No cached town data, request it from server
                safeRequestTownData();
            }

            if (playerTown != null) {
                // Request claim history from server
                com.pokecobble.town.network.SimpleClaimHistorySync.requestClaimHistory(playerTown.getId());

                // Request claim count from server (using same pattern as claim history)
                com.pokecobble.town.network.SimpleClaimCountSync.requestClaimCount(playerTown.getId());

                // Request jobs data from the server
                com.pokecobble.town.client.ClientTownJobsManager.getInstance().requestJobsData();

                // Request only the player's town settings (much more efficient)
                safeRequestConfigSync();

                // Simple image handling - no complex tracking needed
            }
        }

        // We'll use a custom back button instead of the default ButtonWidget
        // The button will be drawn in the render method and handled in mouseClicked

        // Create search field for the Players subcategory
        searchField = new TextFieldWidget(this.textRenderer, 0, 0, 140, 16, Text.literal("Search"));
        searchField.setMaxLength(50);
        searchField.setVisible(false); // Only visible when Players subcategory is selected
        searchField.setChangedListener(this::onSearchFieldChanged);
        addDrawableChild(searchField);

        // Setup subcategories
        setupSubcategories();
    }

    /**
     * Sets up the subcategories for the town management interface.
     */
    private void setupSubcategories() {
        subcategories.clear();

        // Get player town from client-side manager
        Town playerTown = null;
        boolean isDataLoading = false;
        if (client.player != null) {
            playerTown = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTown();
            // If playerTown is null but we have a playerTownId, it means the cache expired
            // Request fresh data from the server but continue to show subcategories
            UUID playerTownId = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTownId();
            if (playerTown == null && playerTownId != null) {
                safeRequestTownData();
                isDataLoading = true; // Mark that we're waiting for data
                // Don't return early - continue to show subcategories
            }
        }

        // Check if there's an active election using client-side manager
        boolean hasElection = false;
        if (playerTown != null) {
            Election election = com.pokecobble.town.client.ClientElectionManager.getInstance().getElection(playerTown.getId());
            hasElection = (election != null);

            // Force refresh of player list when election status changes
            if (hasElection != subcategories.stream().anyMatch(sc -> sc.getName().equals("Election"))) {
                refreshPlayerList();
            }
        }

        // Add subcategories - always show them even when data is loading
        subcategories.add(new TownSubcategory("Main", "🏠", 0xFF2196F3)); // House icon

        // Replace Players with Election during an election (only if we have valid data)
        if (!isDataLoading && hasElection) {
            subcategories.add(new TownSubcategory("Election", "🗳", 0xFFE91E63)); // Ballot box icon
        } else {
            subcategories.add(new TownSubcategory("Players", "👥", 0xFF4CAF50)); // People icon
        }

        subcategories.add(new TownSubcategory("Claims", "🔒", 0xFFE91E63)); // Lock icon
        subcategories.add(new TownSubcategory("Jobs", "💼", 0xFF00BCD4)); // Briefcase icon
        subcategories.add(new TownSubcategory("Level", "⭐", 0xFFFFAA00)); // Star icon
        subcategories.add(new TownSubcategory("Bank", "💰", 0xFFFFD700)); // Money bag icon
        subcategories.add(new TownSubcategory("Chat", "💬", 0xFF9C27B0)); // Chat icon
        subcategories.add(new TownSubcategory("Settings", "⚙", 0xFF9E9E9E)); // Gear icon

        // Select the first subcategory by default
        if (!subcategories.isEmpty()) {
            selectedSubcategory = subcategories.get(0);
        } else {
            selectedSubcategory = null;
        }

        // Initialize subcategory positions
        initializeSubcategoryPositions();
    }

    /**
     * Initializes the positions of subcategory buttons.
     * This ensures they're positioned correctly for click detection before the first render.
     * Note: With scrolling, positions are now calculated dynamically in render and click methods.
     */
    private void initializeSubcategoryPositions() {
        // With scrollable sidebar, positions are calculated dynamically
        // This method is kept for compatibility but positions are now handled in render/click methods
        for (TownSubcategory subcategory : subcategories) {
            // Set a default position - actual positions calculated during render
            subcategory.setPosition(0, 0);
        }
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Update rank name hover animation
        long currentTime = System.currentTimeMillis();
        if (lastAnimationTime == 0) lastAnimationTime = currentTime;
        float deltaTime = (currentTime - lastAnimationTime) / 1000.0f;
        lastAnimationTime = currentTime;

        // Animate rank name appearance/disappearance (disable when context menu is active)
        if (hoveredPlayerUUID != null && !showContextMenu) {
            rankNameAnimation = Math.min(1.0f, rankNameAnimation + deltaTime * 4.0f); // Animate in over 0.25 seconds
        } else {
            rankNameAnimation = Math.max(0.0f, rankNameAnimation - deltaTime * 6.0f); // Animate out faster
        }

        // Render dark background
        this.renderBackground(context);

        // Calculate positions
        int leftX = (width - panelWidth) / 2;
        int topY = 10;

        // Draw glass effect panel matching subcategory style
        drawGlassPanel(context, leftX, topY, panelWidth, panelHeight);

        // Draw glass effect header - slimmer for more space
        int headerHeight = 24; // Reduced from 32 to 24 for more subcategory space
        drawGlassHeader(context, leftX, topY, panelWidth, headerHeight);

        // Get player town from client-side manager
        Town playerTown = null;
        boolean isDataLoading = false;
        if (client.player != null) {
            playerTown = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTown();
            // If playerTown is null but we have a playerTownId, it means the cache expired
            // Request fresh data from the server but continue to show the UI
            UUID playerTownId = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTownId();
            if (playerTown == null && playerTownId != null) {
                // Request fresh town data from server
                safeRequestTownData();
                isDataLoading = true; // Mark that we're waiting for data
                // Don't return early - continue to show the UI with loading indicators
            }
        }

        if (playerTown == null) {
            // If player is no longer in a town and we're not loading data, close the screen
            if (!isDataLoading) {
                this.close();
                return;
            }

            // Show loading message while waiting for data
            context.drawCenteredTextWithShadow(this.textRenderer,
                isDataLoading ? "Loading town data..." : "You are not a member of any town",
                width / 2, height / 2, 0xFFFFFF);
        } else {
            // Calculate content area dimensions - adjusted for new header height
            int contentX = leftX + 10;
            int contentY = topY + headerHeight + 5; // Start after modern header with small gap
            int contentWidth = panelWidth - 20;
            int contentHeight = panelHeight - (headerHeight + 15); // Account for header and bottom margin

            // Calculate sidebar dimensions - narrower and moved to the left
            int sidebarWidth = 90; // Reduced from 130 to 90
            int sidebarX = leftX + 5; // Moved more to the left (from contentX to leftX + 5)
            int sidebarY = contentY;
            int sidebarHeight = contentHeight;

            // Draw glass effect sidebar background matching subcategory style
            context.fill(sidebarX, sidebarY, sidebarX + sidebarWidth, sidebarY + sidebarHeight, GLASS_SIDEBAR_BG);

            // Glass effect borders
            context.fill(sidebarX, sidebarY, sidebarX + sidebarWidth, sidebarY + 1, GLASS_BRIGHT_HIGHLIGHT); // Top highlight
            context.fill(sidebarX, sidebarY, sidebarX + 1, sidebarY + sidebarHeight, GLASS_INNER_HIGHLIGHT); // Left highlight
            context.fill(sidebarX + sidebarWidth - 1, sidebarY, sidebarX + sidebarWidth, sidebarY + sidebarHeight, GLASS_TOP_HIGHLIGHT); // Right highlight
            context.fill(sidebarX, sidebarY + sidebarHeight - 1, sidebarX + sidebarWidth, sidebarY + sidebarHeight, GLASS_SHADOW); // Bottom shadow

            // Inner glass effect for depth
            context.fill(sidebarX + 1, sidebarY + 1, sidebarX + sidebarWidth - 1, sidebarY + 2, GLASS_TOP_HIGHLIGHT);
            context.fill(sidebarX + 1, sidebarY + 1, sidebarX + 2, sidebarY + sidebarHeight - 1, 0x15FFFFFF);

            // Calculate total height of all subcategories for scrolling
            int subcategoryHeight = 26; // Increased from 22 to 26
            int subcategorySpacing = 3; // Increased from 2 to 3
            int totalSubcategoriesHeight = subcategories.size() * (subcategoryHeight + subcategorySpacing);
            if (subcategories.size() > 0) {
                totalSubcategoriesHeight -= subcategorySpacing; // Remove spacing after last subcategory
            }

            // Calculate max scroll for sidebar
            int availableSidebarHeight = sidebarHeight - 10; // Account for top/bottom margins
            int maxSidebarScroll = Math.max(0, totalSubcategoriesHeight - availableSidebarHeight);
            sidebarScrollOffset = Math.min(sidebarScrollOffset, maxSidebarScroll);

            // Draw scrollbar if needed
            if (maxSidebarScroll > 0) {
                int scrollbarX = sidebarX + sidebarWidth - 8; // Position scrollbar on right edge

                // Draw scrollbar track - thinner and more subtle
                context.fill(scrollbarX, sidebarY + 5, scrollbarX + 4, sidebarY + sidebarHeight - 5, 0x20FFFFFF);

                // Calculate scrollbar height and position
                int scrollbarHeight = Math.max(20, availableSidebarHeight * availableSidebarHeight / (totalSubcategoriesHeight + availableSidebarHeight));

                // Calculate scrollbar position
                float scrollRatio = (float)sidebarScrollOffset / maxSidebarScroll;
                int scrollbarY = sidebarY + 5 + (int)((availableSidebarHeight - scrollbarHeight) * scrollRatio);

                // Ensure scrollbar doesn't go out of bounds
                scrollbarY = Math.max(sidebarY + 5, Math.min(scrollbarY, sidebarY + sidebarHeight - 5 - scrollbarHeight));

                // Draw scrollbar handle
                drawRoundedRect(context, scrollbarX, scrollbarY, 4, scrollbarHeight, 0xC0FFFFFF);
            }

            // Apply scissor to clip subcategory buttons to visible area
            context.enableScissor(
                sidebarX,
                sidebarY,
                sidebarX + sidebarWidth - (maxSidebarScroll > 0 ? 8 : 0), // Leave space for scrollbar
                sidebarY + sidebarHeight
            );

            // Draw subcategory buttons with scrolling
            int subcategoryY = sidebarY + 5 - sidebarScrollOffset;

            for (TownSubcategory subcategory : subcategories) {
                // Store position for click detection
                subcategory.setPosition(sidebarX + 5, subcategoryY);

                // Check if this subcategory is selected
                boolean isSelected = subcategory == selectedSubcategory;

                // Check if mouse is hovering over this subcategory
                boolean isHovered = mouseX >= sidebarX + 5 && mouseX <= sidebarX + sidebarWidth - 5 &&
                                   mouseY >= subcategoryY && mouseY <= subcategoryY + subcategoryHeight;

                // Draw glass effect subcategory button background
                int bgColor;
                if (isSelected) {
                    bgColor = subcategory.getColor();
                } else if (isHovered) {
                    bgColor = GLASS_CARD_HOVER;
                } else {
                    bgColor = GLASS_CARD_BG;
                }

                // Draw button background with glass effect
                context.fill(sidebarX + 5, subcategoryY, sidebarX + sidebarWidth - 5, subcategoryY + subcategoryHeight, bgColor);

                // Glass effect highlights - matching subcategory style
                context.fill(sidebarX + 5, subcategoryY, sidebarX + sidebarWidth - 5, subcategoryY + 1, GLASS_TOP_HIGHLIGHT);
                context.fill(sidebarX + 5, subcategoryY, sidebarX + 6, subcategoryY + subcategoryHeight, GLASS_TOP_HIGHLIGHT);
                context.fill(sidebarX + 5, subcategoryY + subcategoryHeight - 1, sidebarX + sidebarWidth - 5, subcategoryY + subcategoryHeight, GLASS_BOTTOM_SHADOW);
                context.fill(sidebarX + sidebarWidth - 6, subcategoryY, sidebarX + sidebarWidth - 5, subcategoryY + subcategoryHeight, GLASS_BOTTOM_SHADOW);

                // Draw subcategory name with icon - compact for narrower sidebar
                String subcategoryText = subcategory.getIcon() + " " + subcategory.getName();
                context.drawTextWithShadow(this.textRenderer, subcategoryText,
                    sidebarX + 8, subcategoryY + (subcategoryHeight - 8) / 2, 0xFFFFFF);

                // Move to next subcategory
                subcategoryY += subcategoryHeight + subcategorySpacing;
            }

            // Disable scissor
            context.disableScissor();

            // Draw content area - adjusted for moved sidebar
            int contentAreaX = sidebarX + sidebarWidth + 5; // Position after sidebar
            int contentAreaWidth = leftX + panelWidth - contentAreaX - 10; // Fill remaining width

            // Draw glass effect content area background matching subcategory style
            context.fill(contentAreaX, sidebarY, contentAreaX + contentAreaWidth, sidebarY + sidebarHeight, GLASS_CONTENT_BG);

            // Glass effect borders
            context.fill(contentAreaX, sidebarY, contentAreaX + contentAreaWidth, sidebarY + 1, GLASS_BRIGHT_HIGHLIGHT); // Top border
            context.fill(contentAreaX, sidebarY, contentAreaX + 1, sidebarY + sidebarHeight, GLASS_BRIGHT_HIGHLIGHT); // Left border

            // Hide search field by default (only show in Players subcategory)
            searchField.setVisible(false);

            // Render the selected subcategory content
            if (selectedSubcategory != null) {
                String subcategoryName = selectedSubcategory.getName();

                // Render the subcategory content
                if (!subcategories.isEmpty() && selectedSubcategory == subcategories.get(0)) {
                    // Store the circle position as class variables for click detection in mouseClicked
                    // These should match the values in renderMainSubcategory
                    circleSize = 50; // Size of the circular shape (slightly smaller)
                    circleX = contentAreaX + 10; // Position more to the left
                    circleY = sidebarY + 20; // Position slightly down

                    // Calculate and store circle center and radius
                    circleCenterX = circleX + circleSize/2;
                    circleCenterY = circleY + circleSize/2;
                    circleRadius = circleSize/2;

                    renderMainSubcategory(context, mouseX, mouseY, playerTown, contentAreaX, sidebarY, contentAreaWidth, sidebarHeight, isDataLoading);
                } else if (subcategoryName.equals("Players")) {
                    if (hasPermissionForSubcategory(subcategoryName, playerTown)) {
                        renderPlayersSubcategory(context, mouseX, mouseY, playerTown, contentAreaX, sidebarY, contentAreaWidth, sidebarHeight);
                    } else {
                        renderPermissionDeniedMessage(context, subcategoryName, contentAreaX, sidebarY, contentAreaWidth, sidebarHeight);
                    }
                } else if (subcategoryName.equals("Election")) {
                    if (hasPermissionForSubcategory(subcategoryName, playerTown)) {
                        renderElectionSubcategory(context, mouseX, mouseY, playerTown, contentAreaX, sidebarY, contentAreaWidth, sidebarHeight);
                    } else {
                        renderPermissionDeniedMessage(context, subcategoryName, contentAreaX, sidebarY, contentAreaWidth, sidebarHeight);
                    }
                } else if (subcategoryName.equals("Claims")) {
                    if (hasPermissionForSubcategory(subcategoryName, playerTown)) {
                        renderClaimsSubcategory(context, mouseX, mouseY, playerTown, contentAreaX, sidebarY, contentAreaWidth, sidebarHeight);
                    } else {
                        renderPermissionDeniedMessage(context, subcategoryName, contentAreaX, sidebarY, contentAreaWidth, sidebarHeight);
                    }
                } else if (subcategoryName.equals("Jobs")) {
                    if (hasPermissionForSubcategory(subcategoryName, playerTown)) {
                        renderJobsSubcategory(context, mouseX, mouseY, playerTown, contentAreaX, sidebarY, contentAreaWidth, sidebarHeight);
                    } else {
                        renderPermissionDeniedMessage(context, subcategoryName, contentAreaX, sidebarY, contentAreaWidth, sidebarHeight);
                    }
                } else if (subcategoryName.equals("Level")) {
                    // Temporarily show a placeholder message instead of the actual level subcategory
                    renderLevelPlaceholderMessage(context, contentAreaX, sidebarY, contentAreaWidth, sidebarHeight);

                    // Original code (commented out for easy restoration later)
                    /*
                    if (hasPermissionForSubcategory(subcategoryName, playerTown)) {
                        renderLevelSubcategory(context, mouseX, mouseY, playerTown, contentAreaX, sidebarY, contentAreaWidth, sidebarHeight);
                    } else {
                        renderPermissionDeniedMessage(context, subcategoryName, contentAreaX, sidebarY, contentAreaWidth, sidebarHeight);
                    }
                    */
                } else if (subcategoryName.equals("Bank")) {
                    if (hasPermissionForSubcategory(subcategoryName, playerTown)) {
                        renderBankSubcategory(context, mouseX, mouseY, playerTown, contentAreaX, sidebarY, contentAreaWidth, sidebarHeight);
                    } else {
                        renderPermissionDeniedMessage(context, subcategoryName, contentAreaX, sidebarY, contentAreaWidth, sidebarHeight);
                    }
                } else if (subcategoryName.equals("Chat")) {
                    if (hasPermissionForSubcategory(subcategoryName, playerTown)) {
                        renderChatSubcategory(context, mouseX, mouseY, playerTown, contentAreaX, sidebarY, contentAreaWidth, sidebarHeight);
                    } else {
                        renderPermissionDeniedMessage(context, subcategoryName, contentAreaX, sidebarY, contentAreaWidth, sidebarHeight);
                    }
                } else if (subcategoryName.equals("Settings")) {
                    if (hasPermissionForSubcategory(subcategoryName, playerTown)) {
                        renderSettingsSubcategory(context, mouseX, mouseY, playerTown, contentAreaX, sidebarY, contentAreaWidth, sidebarHeight);
                    } else {
                        renderPermissionDeniedMessage(context, subcategoryName, contentAreaX, sidebarY, contentAreaWidth, sidebarHeight);
                    }
                }
            }
        }

        // Draw status message if present
        if (statusText != null && !statusText.getString().isEmpty()) {
            context.drawCenteredTextWithShadow(this.textRenderer, statusText, this.width / 2, 5, statusColor);
        }

        super.render(context, mouseX, mouseY, delta);

        // Update and render permission notifications
        updatePermissionNotification();
        renderPermissionNotification(context);

        // Render context menu on top of everything else with higher z-level
        if (showContextMenu && contextMenuPlayer != null && playerTown != null) {
            // Push matrix and translate to higher z-level to ensure it renders on top
            context.getMatrices().push();
            context.getMatrices().translate(0, 0, 1000); // Move to much higher z-level
            renderPlayerContextMenu(context, mouseX, mouseY, playerTown);
            context.getMatrices().pop();
        }
    }

    /**
     * Renders the Main subcategory content.
     */
    private void renderMainSubcategory(DrawContext context, int mouseX, int mouseY, Town playerTown, int contentX, int contentY, int contentWidth, int contentHeight, boolean isDataLoading) {
        // If data is loading, show loading message
        if (isDataLoading || playerTown == null) {
            String message = isDataLoading ? "Loading town data..." : "No town data available";
            int messageWidth = this.textRenderer.getWidth(message);
            context.drawTextWithShadow(this.textRenderer, message,
                contentX + (contentWidth - messageWidth) / 2, contentY + contentHeight / 2, 0xFFFFFF);
            return;
        }

        // Calculate positions for the circular shape and town info
        int circleSize = 50; // Size of the circular shape (slightly smaller)
        int circleX = contentX + 10; // Position the circle more to the left
        int circleY = contentY + 20; // Position slightly down

        // Calculate town info position (to the right of the circle)
        int infoX = circleX + circleSize + 15; // Position town info to the right of the circle
        int infoWidth = contentWidth - circleSize - 40; // Width of the info area, leaving space for the circle
        int rowHeight = 16; // Height of each row
        int sectionSpacing = 10; // Spacing between sections
        int currentY = contentY + 12; // Start position from the top (very slightly down)

        // Calculate circle center and radius
        float centerX = circleX + circleSize/2;
        float centerY = circleY + circleSize/2;
        float radius = circleSize/2;

        // Check if mouse is hovering over the circle
        boolean isCircleHovered = isPointInCircle(mouseX, mouseY, (int)centerX, (int)centerY, (int)radius);

        // Use dedicated no-interference image renderer
        IndependentTownImageRenderer.renderTownImage(context, playerTown, circleX, circleY, circleSize,
                                                   (int)centerX, (int)centerY, (int)radius, isCircleHovered);

        // Simple rendering with no interference - just shows town name

        // Draw hover text when hovered
        if (isCircleHovered) {
            String hoverText = "Change Town Image";
            int hoverTextWidth = this.textRenderer.getWidth(hoverText);
            // Position the hover text below the circle
            context.drawTextWithShadow(this.textRenderer, hoverText,
                (int)(centerX - hoverTextWidth/2), (int)(centerY + radius + 5), 0xFFFFFF);
        }

        // Draw town info on the right side
        // Draw town name as header
        String townName = playerTown.getName();
        context.drawTextWithShadow(this.textRenderer, Text.literal(townName).formatted(Formatting.BOLD),
            infoX - 15, currentY, 0xFFFFFF);
        currentY += rowHeight + 5; // Add a bit more space after the town name

        // Draw town info in a grid layout
        // Description
        context.drawTextWithShadow(this.textRenderer, Text.literal("Description:").formatted(Formatting.ITALIC),
            infoX - 5, currentY, 0xAAAAAA);
        String description = playerTown.getDescription();
        // Truncate description if too long
        if (description.length() > 25) {
            description = description.substring(0, 22) + "...";
        }
        context.drawTextWithShadow(this.textRenderer, description,
            infoX + 65, currentY, 0xFFFFFF);
        currentY += rowHeight;

        // Status
        context.drawTextWithShadow(this.textRenderer, Text.literal("Status:").formatted(Formatting.ITALIC),
            infoX - 5, currentY, 0xAAAAAA);
        String statusText;
        int statusColor;

        switch (playerTown.getJoinType()) {
            case OPEN:
                statusText = "Open";
                statusColor = 0x55FF55; // Green
                break;
            case CLOSED:
                statusText = "Closed";
                statusColor = 0xFF5555; // Red
                break;
            case INVITE_ONLY:
                statusText = "Invite Only";
                statusColor = 0x5555FF; // Blue
                break;
            default:
                statusText = "Unknown";
                statusColor = 0xAAAAAA; // Gray
                break;
        }
        context.drawTextWithShadow(this.textRenderer, statusText,
            infoX + 65, currentY, statusColor);
        currentY += rowHeight + 5; // Add a bit more space after the status

        // Player count
        context.drawTextWithShadow(this.textRenderer, Text.literal("Players:").formatted(Formatting.ITALIC),
            infoX - 15, currentY, 0xAAAAAA);
        String playerCountText = playerTown.getPlayerCount() + "/" + playerTown.getMaxPlayers();
        context.drawTextWithShadow(this.textRenderer, playerCountText,
            infoX + 55, currentY, 0xFFFFFF);
        currentY += rowHeight + sectionSpacing;

        // Town Ratings Section - use the full width of the content area
        int remainingHeight = (contentY + contentHeight) - currentY; // Calculate remaining height correctly
        renderTownRatings(context, mouseX, mouseY, playerTown, contentX, currentY, contentWidth, remainingHeight);

        // Leave Town button has been moved to the Settings subcategory
    }

    /**
     * Renders the Players subcategory content with modern design.
     */
    private void renderPlayersSubcategory(DrawContext context, int mouseX, int mouseY, Town playerTown, int contentX, int contentY, int contentWidth, int contentHeight) {
        // Modern layout with integrated header
        int leftMargin = contentX + 5; // Reduced margin for more space
        int headerHeight = 32; // Taller header for better button integration
        int currentY = contentY + 5; // Start closer to top

        // Draw modern header background with glass effect
        context.fill(leftMargin, currentY, leftMargin + contentWidth - 10, currentY + headerHeight, 0x60404040);
        context.fill(leftMargin, currentY, leftMargin + contentWidth - 10, currentY + 1, 0x20FFFFFF); // Top highlight
        context.fill(leftMargin, currentY, leftMargin + 1, currentY + headerHeight, 0x20FFFFFF); // Left highlight

        // Header layout - distribute controls across the header
        int headerPadding = 8;
        int controlY = currentY + (headerHeight - 16) / 2; // Center controls vertically in header
        int controlSpacing = 8;
        int currentX = leftMargin + headerPadding;

        // Search field - larger and more prominent
        int searchFieldWidth = 140;
        int searchFieldHeight = 16; // Height reference for positioning consistency
        searchField.setX(currentX);
        searchField.setY(controlY);
        searchField.setWidth(searchFieldWidth);
        searchField.setVisible(true);
        currentX += searchFieldWidth + controlSpacing;

        // Sort button with smooth expandable animation
        String sortText;
        String sortIcon;
        int sortIconColor;
        switch (currentSortType) {
            case RANK:
                sortText = "Rank";
                sortIcon = "♦"; // Diamond for rank hierarchy
                sortIconColor = 0xFFD700; // Gold color for rank
                break;
            case NAME:
                sortText = "Name";
                sortIcon = "▲"; // Triangle for alphabetical
                sortIconColor = 0x87CEEB; // Sky blue for names
                break;
            case NEWEST:
                sortText = "New";
                sortIcon = "↑"; // Up arrow for newest first
                sortIconColor = 0x90EE90; // Light green for new
                break;
            case OLDEST:
                sortText = "Old";
                sortIcon = "↓"; // Down arrow for oldest first
                sortIconColor = 0xFFA07A; // Light salmon for old
                break;
            default:
                sortText = "Sort";
                sortIcon = "≡"; // Menu lines for general sort
                sortIconColor = 0xC0C0C0; // Silver for default
                break;
        }

        // Calculate button dimensions with better proportions
        int sortIconWidth = 24; // Larger icon-only width for better appearance
        int sortFullWidth = 8 + this.textRenderer.getWidth(sortIcon) + 6 + this.textRenderer.getWidth(sortText) + 8; // Full expanded width with padding
        int sortButtonHeight = 18; // Slightly taller for better proportions

        boolean currentSortHovered = mouseX >= currentX && mouseX <= currentX + sortIconWidth &&
                                    mouseY >= controlY && mouseY <= controlY + sortButtonHeight;

        // Update hover state and animation
        if (currentSortHovered != sortButtonHovered) {
            sortButtonHovered = currentSortHovered;
        }

        // Smooth animation progress (0.0 = collapsed, 1.0 = expanded)
        float animationSpeed = 0.15f;
        if (sortButtonHovered) {
            sortButtonAnimation = Math.min(1.0f, sortButtonAnimation + animationSpeed);
        } else {
            sortButtonAnimation = Math.max(0.0f, sortButtonAnimation - animationSpeed);
        }

        // Interpolate width smoothly
        int sortCurrentWidth = (int)(sortIconWidth + (sortFullWidth - sortIconWidth) * sortButtonAnimation);

        // Draw sort button with enhanced styling
        int sortCardColor = sortButtonHovered ? 0x70505050 : 0x50404040; // Enhanced glass effect
        context.fill(currentX, controlY, currentX + sortCurrentWidth, controlY + sortButtonHeight, sortCardColor);

        // Enhanced glass highlights with rounded corners effect
        context.fill(currentX + 1, controlY + 1, currentX + sortCurrentWidth - 1, controlY + 2, 0x30FFFFFF);
        context.fill(currentX + 1, controlY + 1, currentX + 2, controlY + sortButtonHeight - 1, 0x20FFFFFF);

        // Enhanced shadow
        context.fill(currentX + 1, controlY + sortButtonHeight - 2, currentX + sortCurrentWidth - 1, controlY + sortButtonHeight - 1, 0x40000000);

        // Draw sort icon centered in collapsed state
        int sortIconX = currentX + (sortIconWidth - this.textRenderer.getWidth(sortIcon)) / 2;
        int sortTextY = controlY + (sortButtonHeight - 8) / 2;

        // Always draw icon
        context.drawTextWithShadow(this.textRenderer, sortIcon, sortIconX, sortTextY, sortIconColor);

        // Draw text with smooth fade-in animation
        if (sortButtonAnimation > 0.1f) {
            int sortTextX = currentX + sortIconWidth + 2;
            // Calculate text opacity based on animation progress
            float textAlpha = Math.max(0.0f, (sortButtonAnimation - 0.3f) / 0.7f);
            int alpha = (int)(255 * textAlpha);
            int textColor = (alpha << 24) | 0x00FFFFFF;
            context.drawTextWithShadow(this.textRenderer, sortText, sortTextX, sortTextY, textColor);
        }

        currentX += sortCurrentWidth + controlSpacing;

        // Filter button with smooth expandable animation
        String filterText;
        String filterIcon;
        int filterIconColor;
        switch (currentFilterType) {
            case ALL:
                filterText = "All";
                filterIcon = "●";
                filterIconColor = 0xFFFFFF; // White
                break;
            case ONLINE:
                filterText = "Online";
                filterIcon = "●";
                filterIconColor = 0x55FF55; // Green (same as player online status)
                break;
            case OFFLINE:
                filterText = "Offline";
                filterIcon = "○";
                filterIconColor = 0x888888; // Gray (same as player offline status)
                break;
            default:
                filterText = "Filter";
                filterIcon = "●";
                filterIconColor = 0x888888; // Gray
                break;
        }

        // Calculate button dimensions with better proportions
        int filterIconWidth = 24; // Larger icon-only width for better appearance
        int filterFullWidth = 8 + this.textRenderer.getWidth(filterIcon) + 6 + this.textRenderer.getWidth(filterText) + 8; // Full expanded width with padding
        int filterButtonHeight = 18; // Slightly taller for better proportions

        boolean currentFilterHovered = mouseX >= currentX && mouseX <= currentX + filterIconWidth &&
                                      mouseY >= controlY && mouseY <= controlY + filterButtonHeight;

        // Update hover state and animation
        if (currentFilterHovered != filterButtonHovered) {
            filterButtonHovered = currentFilterHovered;
        }

        // Smooth animation progress (0.0 = collapsed, 1.0 = expanded)
        float filterAnimationSpeed = 0.15f;
        if (filterButtonHovered) {
            filterButtonAnimation = Math.min(1.0f, filterButtonAnimation + filterAnimationSpeed);
        } else {
            filterButtonAnimation = Math.max(0.0f, filterButtonAnimation - filterAnimationSpeed);
        }

        // Interpolate width smoothly
        int filterCurrentWidth = (int)(filterIconWidth + (filterFullWidth - filterIconWidth) * filterButtonAnimation);

        // Draw filter button with enhanced styling
        int filterCardColor = filterButtonHovered ? 0x70505050 : 0x50404040; // Enhanced glass effect
        context.fill(currentX, controlY, currentX + filterCurrentWidth, controlY + filterButtonHeight, filterCardColor);

        // Enhanced glass highlights with rounded corners effect
        context.fill(currentX + 1, controlY + 1, currentX + filterCurrentWidth - 1, controlY + 2, 0x30FFFFFF);
        context.fill(currentX + 1, controlY + 1, currentX + 2, controlY + filterButtonHeight - 1, 0x20FFFFFF);

        // Enhanced shadow
        context.fill(currentX + 1, controlY + filterButtonHeight - 2, currentX + filterCurrentWidth - 1, controlY + filterButtonHeight - 1, 0x40000000);

        // Draw filter icon centered in collapsed state
        int filterIconX = currentX + (filterIconWidth - this.textRenderer.getWidth(filterIcon)) / 2;
        int filterTextY = controlY + (filterButtonHeight - 8) / 2;

        // Always draw icon
        context.drawTextWithShadow(this.textRenderer, filterIcon, filterIconX, filterTextY, filterIconColor);

        // Draw text with smooth fade-in animation
        if (filterButtonAnimation > 0.1f) {
            int filterTextX = currentX + filterIconWidth + 2;
            // Calculate text opacity based on animation progress
            float textAlpha = Math.max(0.0f, (filterButtonAnimation - 0.3f) / 0.7f);
            int alpha = (int)(255 * textAlpha);
            int textColor = (alpha << 24) | 0x00FFFFFF;
            context.drawTextWithShadow(this.textRenderer, filterText, filterTextX, filterTextY, textColor);
        }

        currentX += filterCurrentWidth + controlSpacing;

        // Invite button - always show, check permission on click
        {
            // Invite button with smooth expandable animation
            String inviteIcon = "+"; // Plus symbol for adding/inviting
            String inviteText = "Invite";
            int inviteIconColor = 0x98FB98; // Pale green for positive action

            // Calculate button dimensions with better proportions
            int inviteIconWidth = 24; // Larger icon-only width for better appearance
            int inviteFullWidth = 8 + this.textRenderer.getWidth(inviteIcon) + 6 + this.textRenderer.getWidth(inviteText) + 8; // Full expanded width with padding
            int inviteButtonHeight = 18; // Slightly taller for better proportions

            boolean currentInviteHovered = mouseX >= currentX && mouseX <= currentX + inviteIconWidth &&
                                          mouseY >= controlY && mouseY <= controlY + inviteButtonHeight;

            // Update hover state and animation
            if (currentInviteHovered != inviteButtonHovered) {
                inviteButtonHovered = currentInviteHovered;
            }

            // Smooth animation progress (0.0 = collapsed, 1.0 = expanded)
            float inviteAnimationSpeed = 0.15f;
            if (inviteButtonHovered) {
                inviteButtonAnimation = Math.min(1.0f, inviteButtonAnimation + inviteAnimationSpeed);
            } else {
                inviteButtonAnimation = Math.max(0.0f, inviteButtonAnimation - inviteAnimationSpeed);
            }

            // Interpolate width smoothly
            int inviteCurrentWidth = (int)(inviteIconWidth + (inviteFullWidth - inviteIconWidth) * inviteButtonAnimation);

            // Draw invite button with enhanced styling
            int inviteCardColor = inviteButtonHovered ? 0x70505050 : 0x50404040; // Enhanced glass effect
            context.fill(currentX, controlY, currentX + inviteCurrentWidth, controlY + inviteButtonHeight, inviteCardColor);

            // Enhanced glass highlights with rounded corners effect
            context.fill(currentX + 1, controlY + 1, currentX + inviteCurrentWidth - 1, controlY + 2, 0x30FFFFFF);
            context.fill(currentX + 1, controlY + 1, currentX + 2, controlY + inviteButtonHeight - 1, 0x20FFFFFF);

            // Enhanced shadow
            context.fill(currentX + 1, controlY + inviteButtonHeight - 2, currentX + inviteCurrentWidth - 1, controlY + inviteButtonHeight - 1, 0x40000000);

            // Draw invite icon centered in collapsed state
            int inviteIconX = currentX + (inviteIconWidth - this.textRenderer.getWidth(inviteIcon)) / 2;
            int inviteTextY = controlY + (inviteButtonHeight - 8) / 2;

            // Always draw icon
            context.drawTextWithShadow(this.textRenderer, inviteIcon, inviteIconX, inviteTextY, inviteIconColor);

            // Draw text with smooth fade-in animation
            if (inviteButtonAnimation > 0.1f) {
                int inviteTextX = currentX + inviteIconWidth + 2;
                // Calculate text opacity based on animation progress
                float textAlpha = Math.max(0.0f, (inviteButtonAnimation - 0.3f) / 0.7f);
                int alpha = (int)(255 * textAlpha);
                int textColor = (alpha << 24) | 0x00FFFFFF;
                context.drawTextWithShadow(this.textRenderer, inviteText, inviteTextX, inviteTextY, textColor);
            }

            // Store the button bounds for click handling (use icon width for consistent clicking)
            inviteButtonBounds = new int[]{currentX, controlY, inviteIconWidth, inviteButtonHeight};
            currentX += inviteCurrentWidth + controlSpacing;
        }

        // Move to player list area - maximize space usage
        currentY += headerHeight + 5; // Small gap after header

        // Calculate list area dimensions - use almost all remaining space
        int listAreaY = currentY;
        int listAreaHeight = contentHeight - (currentY - contentY) - 10; // Use remaining space with small bottom margin

        // Draw modern list area background with subtle styling
        context.fill(leftMargin, listAreaY, leftMargin + contentWidth - 10, listAreaY + listAreaHeight, 0x30000000);

        // Add subtle border
        context.fill(leftMargin, listAreaY, leftMargin + contentWidth - 10, listAreaY + 1, 0x40FFFFFF); // Top border
        context.fill(leftMargin, listAreaY, leftMargin + 1, listAreaY + listAreaHeight, 0x40FFFFFF); // Left border

        // Create dummy player list for testing
        List<TownPlayer> players = createDummyPlayerList(playerTown);

        // Calculate total height of all players using modern card dimensions
        int cardHeight = 24; // Same as in render method
        int cardSpacing = 2; // Same as in render method
        int totalHeight = players.size() * (cardHeight + cardSpacing);
        if (players.size() > 0) {
            totalHeight -= cardSpacing; // Remove spacing after last card
        }

        // Calculate max scroll
        int maxScroll = Math.max(0, totalHeight - listAreaHeight);
        playersScrollOffset = Math.min(playersScrollOffset, maxScroll);

        // Draw modern scrollbar if needed
        if (maxScroll > 0) {
            int scrollbarX = leftMargin + contentWidth - 15; // Position scrollbar inside the new layout

            // Draw scrollbar track - thinner and more subtle
            context.fill(scrollbarX, listAreaY, scrollbarX + 4, listAreaY + listAreaHeight, 0x20FFFFFF);

            // Calculate scrollbar height and position - ensure it's not too small
            int scrollbarHeight = Math.max(40, listAreaHeight * listAreaHeight / (totalHeight + listAreaHeight));

            // Calculate scrollbar position - ensure it can reach the bottom
            float scrollRatio = (float)playersScrollOffset / maxScroll;
            int scrollbarY = listAreaY + (int)((listAreaHeight - scrollbarHeight) * scrollRatio);

            // Ensure scrollbar doesn't go out of bounds
            scrollbarY = Math.max(listAreaY, Math.min(scrollbarY, listAreaY + listAreaHeight - scrollbarHeight));

            // Draw scrollbar handle - rounded corners effect with more opacity
            drawRoundedRect(context, scrollbarX, scrollbarY, 4, scrollbarHeight, 0xC0FFFFFF);
        }

        // Apply scissor to clip content to visible area
        context.enableScissor(
            leftMargin + 5,
            listAreaY,
            leftMargin + contentWidth - 20,
            listAreaY + listAreaHeight
        );

        // Draw player list with modern card styling
        int playerY = listAreaY - playersScrollOffset;
        int cardWidth = contentWidth - 25; // Optimized width for new layout
        // cardHeight and cardSpacing already defined above for scroll calculation

        for (TownPlayer player : players) {
            // Skip if player is completely outside visible area
            if (playerY + cardHeight < listAreaY || playerY > listAreaY + listAreaHeight) {
                playerY += cardHeight + cardSpacing;
                continue;
            }

            // Check if mouse is hovering over this player card
            boolean isHovered = mouseX >= leftMargin + 5 && mouseX <= leftMargin + 5 + cardWidth &&
                               mouseY >= playerY && mouseY <= playerY + cardHeight &&
                               mouseY >= listAreaY && mouseY <= listAreaY + listAreaHeight;

            // Update hover state for rank name animation (disable when context menu is active)
            if (isHovered && !showContextMenu) {
                if (!player.getUuid().equals(hoveredPlayerUUID)) {
                    hoveredPlayerUUID = player.getUuid();
                    rankNameAnimation = 0.0f; // Reset animation when switching players
                }
            } else if (player.getUuid().equals(hoveredPlayerUUID)) {
                // Check if mouse is still over any player card, if not, clear hover
                boolean stillHovering = false;
                // We'll clear this at the end of the loop if no player is hovered
            }

            // Draw modern player card background with glass effect (like ModernTownScreen)
            // Disable hover effect when context menu is active
            int cardColor = (isHovered && !showContextMenu) ? 0x60404040 : 0x40303030;
            context.fill(leftMargin + 5, playerY, leftMargin + 5 + cardWidth, playerY + cardHeight, cardColor);

            // Draw subtle glass highlight at the top
            context.fill(leftMargin + 5, playerY, leftMargin + 5 + cardWidth, playerY + 1, 0x20FFFFFF);
            context.fill(leftMargin + 5, playerY, leftMargin + 6, playerY + cardHeight, 0x20FFFFFF);

            // Draw subtle shadow at the bottom
            context.fill(leftMargin + 5, playerY + cardHeight - 1, leftMargin + 5 + cardWidth, playerY + cardHeight, 0x20000000);

            // Draw rank icon and name with modern styling
            TownPlayerRank rank = player.getRank();
            int iconX = leftMargin + 10;
            int iconY = playerY + (cardHeight - 8) / 2; // Center vertically

            // Draw rank icon with enhanced visibility
            context.drawTextWithShadow(this.textRenderer, rank.getIcon(),
                iconX, iconY, rank.getColor());

            // Calculate icon width for positioning
            int iconWidth = this.textRenderer.getWidth(rank.getIcon());

            // Animated rank name display
            boolean showRankName = player.getUuid().equals(hoveredPlayerUUID) && rankNameAnimation > 0.0f;
            int rankNameWidth = 0;

            if (showRankName) {
                String rankDisplayName = rank.getDisplayName();
                rankNameWidth = this.textRenderer.getWidth(rankDisplayName);

                // Calculate animated position (slides in from right)
                float animationProgress = rankNameAnimation;
                int targetX = iconX + iconWidth + 4; // Right after icon
                int startX = targetX + 20; // Start 20 pixels to the right
                int animatedX = (int)(startX + (targetX - startX) * animationProgress);

                // Calculate animated opacity
                int alpha = (int)(255 * animationProgress);
                int rankNameColor = (alpha << 24) | (rank.getColor() & 0x00FFFFFF);

                // Draw rank name with animation
                context.drawTextWithShadow(this.textRenderer, rankDisplayName,
                    animatedX, iconY, rankNameColor);
            }

            // Draw player name with modern styling (position adjusts for rank name)
            String playerName = player.getName();
            if (client.player != null && playerName.equals(client.player.getName().getString())) {
                playerName += " (You)";
            }
            int nameX = iconX + iconWidth + 8 + (showRankName ? rankNameWidth + 8 : 0); // Position after icon and rank name
            context.drawTextWithShadow(this.textRenderer, Text.literal(playerName).formatted(Formatting.BOLD),
                nameX, iconY, 0xFFFFFF);

            // Draw modern action buttons (compact icons on the right side) - only info button
            int buttonSize = 16;
            int buttonY = playerY + (cardHeight - buttonSize) / 2; // Center vertically in card
            int buttonStartX = leftMargin + 5 + cardWidth - (buttonSize + 35); // More space to avoid online indicator

            // Info button (always visible)
            boolean infoHovered = mouseX >= buttonStartX && mouseX <= buttonStartX + buttonSize &&
                                 mouseY >= buttonY && mouseY <= buttonY + buttonSize;

            int infoColor = infoHovered ? 0x80555555 : 0x60333333;
            context.fill(buttonStartX, buttonY, buttonStartX + buttonSize, buttonY + buttonSize, infoColor);
            context.drawCenteredTextWithShadow(this.textRenderer, "i",
                buttonStartX + buttonSize / 2, buttonY + (buttonSize - 8) / 2, 0xFFFFFF);

            // Draw online status with modern styling (right side of card, after info button)
            String statusText = player.isOnline() ? "●" : "○"; // Use dots instead of text
            int statusColor = player.isOnline() ? 0x55FF55 : 0x888888;
            int statusX = leftMargin + 5 + cardWidth - 20; // Right side of card
            context.drawTextWithShadow(this.textRenderer, statusText,
                statusX, iconY, statusColor);

            // Manage button removed - functionality moved to right-click context menu

            // Draw tooltip on hover (like ModernTownScreen) - disable when context menu is active
            if (isHovered && !showContextMenu) {
                String tooltip = "Right click for " + playerName + " options";
                context.drawTooltip(this.textRenderer, Text.literal(tooltip), mouseX, mouseY);
            }

            playerY += cardHeight + cardSpacing;
        }

        // Clear hover state if mouse is not over any player card
        boolean anyPlayerHovered = false;
        for (TownPlayer player : players) {
            int checkPlayerY = listAreaY - playersScrollOffset + players.indexOf(player) * (cardHeight + cardSpacing);
            if (checkPlayerY + cardHeight >= listAreaY && checkPlayerY <= listAreaY + listAreaHeight) {
                boolean isPlayerHovered = mouseX >= leftMargin + 5 && mouseX <= leftMargin + 5 + cardWidth &&
                                         mouseY >= checkPlayerY && mouseY <= checkPlayerY + cardHeight &&
                                         mouseY >= listAreaY && mouseY <= listAreaY + listAreaHeight;
                if (isPlayerHovered) {
                    anyPlayerHovered = true;
                    break;
                }
            }
        }
        if (!anyPlayerHovered) {
            hoveredPlayerUUID = null;
        }

        // Disable scissor
        context.disableScissor();
    }

    /**
     * Calculates the adjusted context menu position and dimensions.
     */
    private int[] getContextMenuBounds() {
        // Enhanced design matching player card style with icons
        int menuWidth = 140; // Increased to accommodate icons and better text spacing
        int menuItemHeight = 24; // Increased to match player card height exactly
        String[] menuItems = {"🚫 Ban", "👢 Kick", "⚙ Manage", "💬 Message"}; // Added icons
        int menuHeight = menuItems.length * menuItemHeight;

        // Adjust position to keep menu on screen
        int menuX = contextMenuX;
        int menuY = contextMenuY;

        if (menuX + menuWidth > this.width) {
            menuX = this.width - menuWidth - 5;
        }
        if (menuY + menuHeight > this.height) {
            menuY = this.height - menuHeight - 5;
        }

        return new int[]{menuX, menuY, menuWidth, menuHeight};
    }

    /**
     * Renders the player context menu.
     */
    private void renderPlayerContextMenu(DrawContext context, int mouseX, int mouseY, Town playerTown) {
        if (contextMenuPlayer == null) return;

        // Get menu bounds
        int[] bounds = getContextMenuBounds();
        int menuX = bounds[0];
        int menuY = bounds[1];
        int menuWidth = bounds[2];
        int menuHeight = bounds[3];

        // Context menu dimensions - enhanced design
        int menuItemHeight = 24; // Match player card height
        String[] menuItems = {"🚫 Ban", "👢 Kick", "⚙ Manage", "💬 Message"};

        // Enhanced styling matching player card colors exactly but more visible
        int cardBg = 0xC0303030; // More opaque version of player card background (0x40303030)
        int glassHighlight = 0x40FFFFFF; // Stronger version of player card highlight (0x20FFFFFF)
        int glassShadow = 0x40000000; // Stronger version of player card shadow (0x20000000)

        // Draw enhanced shadow for depth (stronger than player cards for menu visibility)
        context.fill(menuX + 2, menuY + 2, menuX + menuWidth + 2, menuY + menuHeight + 2, 0x80000000);

        // Draw main background matching player card style but more visible
        context.fill(menuX, menuY, menuX + menuWidth, menuY + menuHeight, cardBg);

        // Draw glass effect highlights (same style as player cards but stronger)
        context.fill(menuX, menuY, menuX + menuWidth, menuY + 1, glassHighlight); // Top highlight
        context.fill(menuX, menuY, menuX + 1, menuY + menuHeight, glassHighlight); // Left highlight

        // Draw glass effect shadows (same style as player cards but stronger)
        context.fill(menuX, menuY + menuHeight - 1, menuX + menuWidth, menuY + menuHeight, glassShadow); // Bottom shadow
        context.fill(menuX + menuWidth - 1, menuY, menuX + menuWidth, menuY + menuHeight, glassShadow); // Right shadow

        // Check permissions for each action
        boolean canManagePlayers = false;
        if (client.player != null && playerTown != null) {
            TownPlayer currentTownPlayer = playerTown.getPlayer(client.player.getUuid());
            if (currentTownPlayer != null) {
                canManagePlayers = currentTownPlayer.getRank().hasAdminPermissions() ||
                                   currentTownPlayer.hasPermission("Player Management", "Can manage player permissions");
            }
        }

        // Draw menu items with enhanced styling matching player cards
        for (int i = 0; i < menuItems.length; i++) {
            int itemY = menuY + i * menuItemHeight;
            boolean isHovered = mouseX >= menuX && mouseX <= menuX + menuWidth &&
                               mouseY >= itemY && mouseY <= itemY + menuItemHeight;

            // Determine if item is enabled
            boolean isEnabled = true;
            String displayText = menuItems[i];
            if (i == 0 || i == 1) { // Ban/Kick
                isEnabled = canManagePlayers && !contextMenuPlayer.getRank().hasAdminPermissions();
            } else if (i == 2) { // Manage Permissions
                isEnabled = canManagePlayers && !contextMenuPlayer.getRank().hasAdminPermissions();
            } else if (i == 3) { // Private Message
                isEnabled = false; // Always disabled as marked unavailable
                displayText = "💬 Message"; // Keep icon for consistency
            }

            // Draw hover effect matching player card style but more prominent
            if (isHovered && isEnabled) {
                int buttonX = menuX + 1;
                int buttonY = itemY;
                int buttonWidth = menuWidth - 2;
                int buttonHeight = menuItemHeight;

                // Enhanced hover background matching player card hover (0x60404040) but more visible
                context.fill(buttonX, buttonY, buttonX + buttonWidth, buttonY + buttonHeight, 0x80404040);

                // Glass button highlights matching player card style
                context.fill(buttonX, buttonY, buttonX + buttonWidth, buttonY + 1, 0x40FFFFFF); // Top highlight
                context.fill(buttonX, buttonY, buttonX + 1, buttonY + buttonHeight, 0x40FFFFFF); // Left highlight

                // Glass button shadows matching player card style
                context.fill(buttonX, buttonY + buttonHeight - 1, buttonX + buttonWidth, buttonY + buttonHeight, 0x40000000); // Bottom shadow
                context.fill(buttonX + buttonWidth - 1, buttonY, buttonX + buttonWidth, buttonY + buttonHeight, 0x40000000); // Right shadow
            } else if (isHovered && !isEnabled) {
                // Disabled hover effect with consistent styling
                int buttonX = menuX + 1;
                int buttonY = itemY;
                int buttonWidth = menuWidth - 2;
                int buttonHeight = menuItemHeight;
                context.fill(buttonX, buttonY, buttonX + buttonWidth, buttonY + buttonHeight, 0x60333333);

                // Add subtle highlights for disabled state
                context.fill(buttonX, buttonY, buttonX + buttonWidth, buttonY + 1, 0x20FFFFFF);
                context.fill(buttonX, buttonY, buttonX + 1, buttonY + buttonHeight, 0x20FFFFFF);
            }

            // Enhanced text rendering with better contrast and visibility
            int textColor;
            if (isEnabled) {
                textColor = isHovered ? 0xFFFFFFFF : 0xFFF0F0F0; // Brighter text for better visibility
            } else {
                textColor = 0xFF999999; // Lighter gray for disabled but still visible
            }

            // Center text vertically in the menu item with better spacing
            int textY = itemY + (menuItemHeight - 8) / 2;

            // Add text shadow for better readability on the enhanced background
            context.drawTextWithShadow(this.textRenderer, displayText,
                menuX + 10, textY, textColor); // Increased left padding for better icon spacing and visibility
        }
    }

    /**
     * Handles context menu clicks.
     */
    private boolean handleContextMenuClick(double mouseX, double mouseY, int button, Town playerTown) {
        if (contextMenuPlayer == null || button != 0) return false; // Only handle left clicks

        // Get menu bounds using the same calculation as rendering
        int[] bounds = getContextMenuBounds();
        int menuX = bounds[0];
        int menuY = bounds[1];
        int menuWidth = bounds[2];
        int menuHeight = bounds[3];
        int menuItemHeight = 24; // Updated to match new design

        // Check if click is within menu bounds
        if (mouseX >= menuX && mouseX <= menuX + menuWidth &&
            mouseY >= menuY && mouseY <= menuY + menuHeight) {

            // Determine which item was clicked
            int clickedItem = (int)((mouseY - menuY) / menuItemHeight);
            String[] menuItems = {"🚫 Ban", "👢 Kick", "⚙ Manage", "💬 Message"}; // Updated with icons

            if (clickedItem >= 0 && clickedItem < menuItems.length) {
                handleContextMenuAction(clickedItem, playerTown);
            }

            // Close context menu
            showContextMenu = false;
            contextMenuPlayer = null;
            return true;
        }

        return false;
    }

    /**
     * Handles context menu actions.
     */
    private void handleContextMenuAction(int actionIndex, Town playerTown) {
        if (contextMenuPlayer == null) return;

        // Check permissions
        boolean canManagePlayers = false;
        boolean canKickPlayers = false;
        boolean canBanPlayers = false;
        TownPlayer currentTownPlayer = null;

        if (client.player != null && playerTown != null) {
            currentTownPlayer = playerTown.getPlayer(client.player.getUuid());
            if (currentTownPlayer != null) {
                // Check if player has admin permissions (owner or admin viewer)
                boolean hasAdminPermissions = currentTownPlayer.getRank().hasAdminPermissions();

                canManagePlayers = hasAdminPermissions || currentTownPlayer.hasPermission("Player Management", "Can manage player permissions");
                canKickPlayers = hasAdminPermissions || currentTownPlayer.hasPermission("Player Management", "Can kick players");
                canBanPlayers = hasAdminPermissions || currentTownPlayer.hasPermission("Player Management", "Can kick players"); // Using kick permission for ban as well
            }
        }

        switch (actionIndex) {
            case 0: // Ban Player
                // ADMIN_VIEWER can ban anyone, others cannot ban admin ranks
                boolean canBanTarget = canBanPlayers &&
                    (currentTownPlayer != null && currentTownPlayer.getRank() == TownPlayerRank.ADMIN_VIEWER || !contextMenuPlayer.getRank().hasAdminPermissions());

                if (canBanTarget) {
                    // Show ban confirmation screen
                    showBanConfirmationScreen(contextMenuPlayer);
                    playClickSound();
                } else {
                    // Show permission notification
                    if (!canBanPlayers) {
                        showPermissionNotification("player banning");
                    } else if (contextMenuPlayer.getRank().hasAdminPermissions()) {
                        showPermissionNotification("managing admin players");
                    }
                }
                break;
            case 1: // Kick Player
                // ADMIN_VIEWER can kick anyone, others cannot kick admin ranks
                boolean canKickTarget = canKickPlayers &&
                    (currentTownPlayer != null && currentTownPlayer.getRank() == TownPlayerRank.ADMIN_VIEWER || !contextMenuPlayer.getRank().hasAdminPermissions());

                if (canKickTarget) {
                    // Show kick confirmation screen
                    showKickConfirmationScreen(contextMenuPlayer);
                    playClickSound();
                } else {
                    // Show permission notification
                    if (!canKickPlayers) {
                        showPermissionNotification("player kicking");
                    } else if (contextMenuPlayer.getRank().hasAdminPermissions()) {
                        showPermissionNotification("managing admin players");
                    }
                }
                break;
            case 2: // Manage Permissions
                // ADMIN_VIEWER can manage anyone, others cannot manage admin ranks
                boolean canManageTarget = canManagePlayers &&
                    (currentTownPlayer != null && currentTownPlayer.getRank() == TownPlayerRank.ADMIN_VIEWER || !contextMenuPlayer.getRank().hasAdminPermissions());

                if (canManageTarget) {
                    playClickSound();
                    // Check permission before opening player management screen
                    if (!PermissionChecker.checkPermissionOrShowNotification(this, playerTown, client.player.getUuid(),
                            PermissionChecker.Permissions.PLAYER_MANAGEMENT,
                            PermissionChecker.Permissions.CAN_MANAGE_PLAYER_PERMISSIONS,
                            PermissionChecker.FeatureNames.PLAYER_MANAGEMENT)) {
                        return; // Permission denied notification was shown
                    }
                    // Permission granted, open player management screen
                    try {
                        this.client.setScreen(new PlayerManageScreen(this, playerTown, contextMenuPlayer));
                    } catch (Exception e) {
                        System.err.println("Error opening PlayerManageScreen: " + e.getMessage());
                        e.printStackTrace();
                        NotificationManager.getInstance().addErrorNotification("Failed to open player management screen");
                    }
                } else {
                    // Show permission notification
                    if (!canManagePlayers) {
                        showPermissionNotification("player management");
                    } else if (contextMenuPlayer.getRank().hasAdminPermissions()) {
                        showPermissionNotification("managing admin players");
                    }
                }
                break;
            case 3: // Private Message (unavailable)
                NotificationManager.getInstance().addInfoNotification("Private messaging is not available");
                break;
        }
    }

    /**
     * Renders the Claims subcategory content with modern design.
     */
    private void renderClaimsSubcategory(DrawContext context, int mouseX, int mouseY, Town playerTown, int contentX, int contentY, int contentWidth, int contentHeight) {
        // Get fresh town reference to ensure we have the latest claim history data
        // This is important because the ClaimHistorySynchronizer may have updated the town object
        // after the initial playerTown reference was obtained in the render method
        Town freshPlayerTown = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTown();
        if (freshPlayerTown != null) {
            playerTown = freshPlayerTown; // Use the fresh reference
        }

        // Modern layout with integrated header (matching players subcategory)
        int leftMargin = contentX + 5; // Reduced margin for more space
        int headerHeight = 32; // Taller header for better button integration
        int currentY = contentY + 5; // Start closer to top

        // Draw modern header background with glass effect
        context.fill(leftMargin, currentY, leftMargin + contentWidth - 10, currentY + headerHeight, 0x60404040);
        context.fill(leftMargin, currentY, leftMargin + contentWidth - 10, currentY + 1, 0x20FFFFFF); // Top highlight
        context.fill(leftMargin, currentY, leftMargin + 1, currentY + headerHeight, 0x20FFFFFF); // Left highlight

        // Header layout - distribute controls across the header
        int headerPadding = 8;
        int controlY = currentY + (headerHeight - 16) / 2; // Center controls vertically in header
        int controlSpacing = 8;
        int currentX = leftMargin + headerPadding;

        // Claim Tool button with smooth expandable animation
        String claimToolIcon = "⛏"; // Pickaxe icon for claiming
        String claimToolText = "Claim Tool";
        int claimToolIconColor = 0x4CAF50; // Green for positive action

        // Calculate button dimensions with better proportions
        int claimToolIconWidth = 24; // Larger icon-only width for better appearance
        int claimToolFullWidth = 8 + this.textRenderer.getWidth(claimToolIcon) + 6 + this.textRenderer.getWidth(claimToolText) + 8; // Full expanded width with padding
        int claimToolButtonHeight = 18; // Slightly taller for better proportions

        boolean currentClaimToolHovered = mouseX >= currentX && mouseX <= currentX + claimToolIconWidth &&
                                         mouseY >= controlY && mouseY <= controlY + claimToolButtonHeight;

        // Update hover state and animation
        if (currentClaimToolHovered != claimToolButtonHovered) {
            claimToolButtonHovered = currentClaimToolHovered;
        }

        // Smooth animation progress (0.0 = collapsed, 1.0 = expanded)
        float claimToolAnimationSpeed = 0.15f;
        if (claimToolButtonHovered) {
            claimToolButtonAnimation = Math.min(1.0f, claimToolButtonAnimation + claimToolAnimationSpeed);
        } else {
            claimToolButtonAnimation = Math.max(0.0f, claimToolButtonAnimation - claimToolAnimationSpeed);
        }

        // Interpolate width smoothly
        int claimToolCurrentWidth = (int)(claimToolIconWidth + (claimToolFullWidth - claimToolIconWidth) * claimToolButtonAnimation);

        // Draw claim tool button with enhanced styling
        int claimToolCardColor = claimToolButtonHovered ? 0x70505050 : 0x50404040; // Enhanced glass effect
        context.fill(currentX, controlY, currentX + claimToolCurrentWidth, controlY + claimToolButtonHeight, claimToolCardColor);

        // Enhanced glass highlights with rounded corners effect
        context.fill(currentX + 1, controlY + 1, currentX + claimToolCurrentWidth - 1, controlY + 2, 0x30FFFFFF);
        context.fill(currentX + 1, controlY + 1, currentX + 2, controlY + claimToolButtonHeight - 1, 0x20FFFFFF);

        // Enhanced shadow
        context.fill(currentX + 1, controlY + claimToolButtonHeight - 2, currentX + claimToolCurrentWidth - 1, controlY + claimToolButtonHeight - 1, 0x40000000);

        // Draw claim tool icon centered in collapsed state
        int claimToolIconX = currentX + (claimToolIconWidth - this.textRenderer.getWidth(claimToolIcon)) / 2;
        int claimToolTextY = controlY + (claimToolButtonHeight - 8) / 2;

        // Always draw icon
        context.drawTextWithShadow(this.textRenderer, claimToolIcon, claimToolIconX, claimToolTextY, claimToolIconColor);

        // Draw text with smooth fade-in animation
        if (claimToolButtonAnimation > 0.1f) {
            int claimToolTextX = currentX + claimToolIconWidth + 2;
            // Calculate text opacity based on animation progress
            float textAlpha = Math.max(0.0f, (claimToolButtonAnimation - 0.3f) / 0.7f);
            int alpha = (int)(255 * textAlpha);
            int textColor = (alpha << 24) | 0x00FFFFFF;
            context.drawTextWithShadow(this.textRenderer, claimToolText, claimToolTextX, claimToolTextY, textColor);
        }

        currentX += claimToolCurrentWidth + controlSpacing;

        // View Boundaries button with smooth expandable animation
        String viewBoundariesIcon = "👁"; // Eye icon for viewing
        String viewBoundariesText = "View Boundaries";
        int viewBoundariesIconColor = 0x2196F3; // Blue for viewing action

        // Calculate button dimensions with better proportions
        int viewBoundariesIconWidth = 24; // Larger icon-only width for better appearance
        int viewBoundariesFullWidth = 8 + this.textRenderer.getWidth(viewBoundariesIcon) + 6 + this.textRenderer.getWidth(viewBoundariesText) + 8; // Full expanded width with padding
        int viewBoundariesButtonHeight = 18; // Slightly taller for better proportions

        boolean currentViewBoundariesHovered = mouseX >= currentX && mouseX <= currentX + viewBoundariesIconWidth &&
                                              mouseY >= controlY && mouseY <= controlY + viewBoundariesButtonHeight;

        // Update hover state and animation
        if (currentViewBoundariesHovered != viewBoundariesButtonHovered) {
            viewBoundariesButtonHovered = currentViewBoundariesHovered;
        }

        // Smooth animation progress (0.0 = collapsed, 1.0 = expanded)
        float viewBoundariesAnimationSpeed = 0.15f;
        if (viewBoundariesButtonHovered) {
            viewBoundariesButtonAnimation = Math.min(1.0f, viewBoundariesButtonAnimation + viewBoundariesAnimationSpeed);
        } else {
            viewBoundariesButtonAnimation = Math.max(0.0f, viewBoundariesButtonAnimation - viewBoundariesAnimationSpeed);
        }

        // Interpolate width smoothly
        int viewBoundariesCurrentWidth = (int)(viewBoundariesIconWidth + (viewBoundariesFullWidth - viewBoundariesIconWidth) * viewBoundariesButtonAnimation);

        // Draw view boundaries button with enhanced styling
        int viewBoundariesCardColor = viewBoundariesButtonHovered ? 0x70505050 : 0x50404040; // Enhanced glass effect
        context.fill(currentX, controlY, currentX + viewBoundariesCurrentWidth, controlY + viewBoundariesButtonHeight, viewBoundariesCardColor);

        // Enhanced glass highlights with rounded corners effect
        context.fill(currentX + 1, controlY + 1, currentX + viewBoundariesCurrentWidth - 1, controlY + 2, 0x30FFFFFF);
        context.fill(currentX + 1, controlY + 1, currentX + 2, controlY + viewBoundariesButtonHeight - 1, 0x20FFFFFF);

        // Enhanced shadow
        context.fill(currentX + 1, controlY + viewBoundariesButtonHeight - 2, currentX + viewBoundariesCurrentWidth - 1, controlY + viewBoundariesButtonHeight - 1, 0x40000000);

        // Draw view boundaries icon centered in collapsed state
        int viewBoundariesIconX = currentX + (viewBoundariesIconWidth - this.textRenderer.getWidth(viewBoundariesIcon)) / 2;
        int viewBoundariesTextY = controlY + (viewBoundariesButtonHeight - 8) / 2;

        // Always draw icon
        context.drawTextWithShadow(this.textRenderer, viewBoundariesIcon, viewBoundariesIconX, viewBoundariesTextY, viewBoundariesIconColor);

        // Draw text with smooth fade-in animation
        if (viewBoundariesButtonAnimation > 0.1f) {
            int viewBoundariesTextX = currentX + viewBoundariesIconWidth + 2;
            // Calculate text opacity based on animation progress
            float textAlpha = Math.max(0.0f, (viewBoundariesButtonAnimation - 0.3f) / 0.7f);
            int alpha = (int)(255 * textAlpha);
            int textColor = (alpha << 24) | 0x00FFFFFF;
            context.drawTextWithShadow(this.textRenderer, viewBoundariesText, viewBoundariesTextX, viewBoundariesTextY, textColor);
        }

        currentX += viewBoundariesCurrentWidth + controlSpacing;

        // Tag Settings button with smooth expandable animation
        String tagSettingsIcon = "⚙"; // Gear icon for settings
        String tagSettingsText = "Tag Settings";
        int tagSettingsIconColor = 0x9C27B0; // Purple for settings action

        // Calculate button dimensions with better proportions
        int tagSettingsIconWidth = 24; // Larger icon-only width for better appearance
        int tagSettingsFullWidth = 8 + this.textRenderer.getWidth(tagSettingsIcon) + 6 + this.textRenderer.getWidth(tagSettingsText) + 8; // Full expanded width with padding
        int tagSettingsButtonHeight = 18; // Slightly taller for better proportions

        boolean currentTagSettingsHovered = mouseX >= currentX && mouseX <= currentX + tagSettingsIconWidth &&
                                           mouseY >= controlY && mouseY <= controlY + tagSettingsButtonHeight;

        // Update hover state and animation
        if (currentTagSettingsHovered != tagSettingsButtonHovered) {
            tagSettingsButtonHovered = currentTagSettingsHovered;
        }

        // Smooth animation progress (0.0 = collapsed, 1.0 = expanded)
        float tagSettingsAnimationSpeed = 0.15f;
        if (tagSettingsButtonHovered) {
            tagSettingsButtonAnimation = Math.min(1.0f, tagSettingsButtonAnimation + tagSettingsAnimationSpeed);
        } else {
            tagSettingsButtonAnimation = Math.max(0.0f, tagSettingsButtonAnimation - tagSettingsAnimationSpeed);
        }

        // Interpolate width smoothly
        int tagSettingsCurrentWidth = (int)(tagSettingsIconWidth + (tagSettingsFullWidth - tagSettingsIconWidth) * tagSettingsButtonAnimation);

        // Draw tag settings button with enhanced styling
        int tagSettingsCardColor = tagSettingsButtonHovered ? 0x70505050 : 0x50404040; // Enhanced glass effect
        context.fill(currentX, controlY, currentX + tagSettingsCurrentWidth, controlY + tagSettingsButtonHeight, tagSettingsCardColor);

        // Enhanced glass highlights with rounded corners effect
        context.fill(currentX + 1, controlY + 1, currentX + tagSettingsCurrentWidth - 1, controlY + 2, 0x30FFFFFF);
        context.fill(currentX + 1, controlY + 1, currentX + 2, controlY + tagSettingsButtonHeight - 1, 0x20FFFFFF);

        // Enhanced shadow
        context.fill(currentX + 1, controlY + tagSettingsButtonHeight - 2, currentX + tagSettingsCurrentWidth - 1, controlY + tagSettingsButtonHeight - 1, 0x40000000);

        // Draw tag settings icon centered in collapsed state
        int tagSettingsIconX = currentX + (tagSettingsIconWidth - this.textRenderer.getWidth(tagSettingsIcon)) / 2;
        int tagSettingsTextY = controlY + (tagSettingsButtonHeight - 8) / 2;

        // Always draw icon
        context.drawTextWithShadow(this.textRenderer, tagSettingsIcon, tagSettingsIconX, tagSettingsTextY, tagSettingsIconColor);

        // Draw text with smooth fade-in animation
        if (tagSettingsButtonAnimation > 0.1f) {
            int tagSettingsTextX = currentX + tagSettingsIconWidth + 2;
            // Calculate text opacity based on animation progress
            float textAlpha = Math.max(0.0f, (tagSettingsButtonAnimation - 0.3f) / 0.7f);
            int alpha = (int)(255 * textAlpha);
            int textColor = (alpha << 24) | 0x00FFFFFF;
            context.drawTextWithShadow(this.textRenderer, tagSettingsText, tagSettingsTextX, tagSettingsTextY, textColor);
        }

        currentX += tagSettingsCurrentWidth + controlSpacing;

        // Claim Usage widget in header (right side)
        // Use GlobalChunkClaimRegistry as the authoritative source for claim counts
        // to ensure consistency with ModernClaimToolHud
        com.pokecobble.town.client.GlobalChunkClaimRegistry registry =
            com.pokecobble.town.client.GlobalChunkClaimRegistry.getInstance();
        int usedClaims = registry.getReliableClaimCount(playerTown.getId(), playerTown);
        int maxClaims = playerTown.getMaxClaims();

        // Debug logging for claim counter (only log occasionally to avoid spam)
        if (System.currentTimeMillis() % 5000 < 100) { // Log roughly every 5 seconds
            int registryCount = registry.getTownChunkCount(playerTown.getId());
            int townClaimCount = playerTown.getClaimCount();
            if (registryCount != townClaimCount) {
                Pokecobbleclaim.LOGGER.debug("MyTownScreen: Claim count mismatch - Registry: {}, Town: {}, Using: {} for town {}",
                    registryCount, townClaimCount, usedClaims, playerTown.getName());
            } else {
                Pokecobbleclaim.LOGGER.debug("MyTownScreen: Rendering claim counter - {}/{} claims for town {}",
                    usedClaims, maxClaims, playerTown.getName());
            }
        }

        // Position claim usage widget in the right side of header
        int claimUsageWidth = 160;
        int claimUsageHeight = 20;
        int claimUsageX = leftMargin + contentWidth - 10 - claimUsageWidth;
        int claimUsageY = controlY - 2; // Align with other controls

        // Draw claim usage background with glass effect
        context.fill(claimUsageX, claimUsageY, claimUsageX + claimUsageWidth, claimUsageY + claimUsageHeight, 0x50404040);
        context.fill(claimUsageX + 1, claimUsageY + 1, claimUsageX + claimUsageWidth - 1, claimUsageY + 2, 0x30FFFFFF);
        context.fill(claimUsageX + 1, claimUsageY + 1, claimUsageX + 2, claimUsageY + claimUsageHeight - 1, 0x20FFFFFF);

        // Draw claim usage text
        String claimUsageText = usedClaims + "/" + maxClaims + " Claims";
        int textWidth = this.textRenderer.getWidth(claimUsageText);
        int textX = claimUsageX + 8;
        int textY = claimUsageY + (claimUsageHeight - 8) / 2;
        context.drawTextWithShadow(this.textRenderer, claimUsageText, textX, textY, 0xFFFFFF);

        // Draw progress bar
        int progressBarX = textX + textWidth + 8;
        int progressBarY = claimUsageY + 6;
        int progressBarWidth = claimUsageX + claimUsageWidth - progressBarX - 8;
        int progressBarHeight = 8;

        // Progress bar background
        context.fill(progressBarX, progressBarY, progressBarX + progressBarWidth, progressBarY + progressBarHeight, 0x40000000);

        // Progress bar fill
        if (maxClaims > 0) {
            float progress = (float) usedClaims / maxClaims;
            int fillWidth = (int) (progressBarWidth * progress);
            int fillColor = progress > 0.8f ? 0xFFE53935 : (progress > 0.6f ? 0xFFFF9800 : 0xFF4CAF50); // Red if >80%, orange if >60%, green otherwise
            context.fill(progressBarX, progressBarY, progressBarX + fillWidth, progressBarY + progressBarHeight, fillColor);
        }

        // Move to content area - maximize space usage
        currentY += headerHeight + 5; // Small gap after header

        // Calculate content area dimensions - use almost all remaining space
        int contentAreaY = currentY;
        int contentAreaHeight = contentHeight - (currentY - contentY) - 10; // Use remaining space with small bottom margin

        // Draw modern claims history section (now takes full content area)
        int historyAreaY = contentAreaY;
        int historyAreaHeight = contentAreaHeight; // Use all remaining space

        // Draw modern list area background with subtle styling
        context.fill(leftMargin, historyAreaY, leftMargin + contentWidth - 10, historyAreaY + historyAreaHeight, 0x30000000);

        // Add subtle border
        context.fill(leftMargin, historyAreaY, leftMargin + contentWidth - 10, historyAreaY + 1, 0x40FFFFFF); // Top border
        context.fill(leftMargin, historyAreaY, leftMargin + 1, historyAreaY + historyAreaHeight, 0x40FFFFFF); // Left border

        // History header with "see more" button
        int historyHeaderHeight = 24;
        context.drawTextWithShadow(this.textRenderer, Text.literal("Claim History").formatted(Formatting.BOLD),
            leftMargin + 10, historyAreaY + 8, 0xFFFFFF);

        // "See More" button with expandable animation (right side of header)
        String seeMoreIcon = "📋"; // Clipboard icon for history
        String seeMoreText = "See More";
        int seeMoreIconColor = 0x2196F3; // Blue for info action

        // Calculate button dimensions
        int seeMoreIconWidth = 24;
        int seeMoreFullWidth = 8 + this.textRenderer.getWidth(seeMoreIcon) + 6 + this.textRenderer.getWidth(seeMoreText) + 8;
        int seeMoreButtonHeight = 18;

        // Position button in right side of header
        int seeMoreX = leftMargin + contentWidth - 10 - seeMoreIconWidth - 10; // Right aligned with some margin
        int seeMoreY = historyAreaY + (historyHeaderHeight - seeMoreButtonHeight) / 2; // Center vertically in header

        // Check if mouse is over the button
        boolean currentSeeMoreHovered = mouseX >= seeMoreX && mouseX <= seeMoreX + seeMoreIconWidth &&
                                       mouseY >= seeMoreY && mouseY <= seeMoreY + seeMoreButtonHeight;

        // Update hover state and animation
        if (currentSeeMoreHovered != seeMoreButtonHovered) {
            seeMoreButtonHovered = currentSeeMoreHovered;
        }

        // Smooth animation progress
        float seeMoreAnimationSpeed = 0.15f;
        if (seeMoreButtonHovered) {
            seeMoreButtonAnimation = Math.min(1.0f, seeMoreButtonAnimation + seeMoreAnimationSpeed);
        } else {
            seeMoreButtonAnimation = Math.max(0.0f, seeMoreButtonAnimation - seeMoreAnimationSpeed);
        }

        // Interpolate width smoothly
        int seeMoreCurrentWidth = (int)(seeMoreIconWidth + (seeMoreFullWidth - seeMoreIconWidth) * seeMoreButtonAnimation);

        // Draw see more button with enhanced styling
        int seeMoreCardColor = seeMoreButtonHovered ? 0x70505050 : 0x50404040;
        context.fill(seeMoreX, seeMoreY, seeMoreX + seeMoreCurrentWidth, seeMoreY + seeMoreButtonHeight, seeMoreCardColor);

        // Glass highlights
        context.fill(seeMoreX + 1, seeMoreY + 1, seeMoreX + seeMoreCurrentWidth - 1, seeMoreY + 2, 0x30FFFFFF);
        context.fill(seeMoreX + 1, seeMoreY + 1, seeMoreX + 2, seeMoreY + seeMoreButtonHeight - 1, 0x20FFFFFF);

        // Enhanced shadow
        context.fill(seeMoreX + 1, seeMoreY + seeMoreButtonHeight - 2, seeMoreX + seeMoreCurrentWidth - 1, seeMoreY + seeMoreButtonHeight - 1, 0x40000000);

        // Draw icon on the right side when expanded, centered when collapsed
        int seeMoreIconX;
        if (seeMoreButtonAnimation > 0.1f) {
            // When expanded, position icon on the right side
            seeMoreIconX = seeMoreX + seeMoreCurrentWidth - this.textRenderer.getWidth(seeMoreIcon) - 8;
        } else {
            // When collapsed, center the icon
            seeMoreIconX = seeMoreX + (seeMoreIconWidth - this.textRenderer.getWidth(seeMoreIcon)) / 2;
        }
        int seeMoreTextY = seeMoreY + (seeMoreButtonHeight - 8) / 2;
        context.drawTextWithShadow(this.textRenderer, seeMoreIcon, seeMoreIconX, seeMoreTextY, seeMoreIconColor);

        // Draw text with smooth fade-in animation (on the left side of icon)
        if (seeMoreButtonAnimation > 0.1f) {
            int seeMoreTextX = seeMoreX + 8; // Position text on the left side with padding
            float textAlpha = Math.max(0.0f, (seeMoreButtonAnimation - 0.3f) / 0.7f);
            int alpha = (int)(255 * textAlpha);
            int textColor = (alpha << 24) | 0x00FFFFFF;
            context.drawTextWithShadow(this.textRenderer, seeMoreText, seeMoreTextX, seeMoreTextY, textColor);
        }

        // Get real claim history from the town
        // Get fresh claim history from the simple history manager
        List<com.pokecobble.town.claim.SimpleClaimHistoryEntry> claimHistory =
            com.pokecobble.town.claim.SimpleClaimHistoryManager.getInstance().getClaimHistory(playerTown.getId());

        // Calculate list area for scrolling
        int listAreaY = historyAreaY + historyHeaderHeight;
        int listAreaHeight = historyAreaHeight - historyHeaderHeight;

        // Calculate total height of all history entries using modern card dimensions
        int entryCardHeight = 20; // Height for each history entry card
        int entryCardSpacing = 2; // Spacing between cards
        int totalHistoryHeight = claimHistory.size() * (entryCardHeight + entryCardSpacing);
        if (claimHistory.size() > 0) {
            totalHistoryHeight -= entryCardSpacing; // Remove spacing after last card
        }

        // Calculate max scroll
        int maxHistoryScroll = Math.max(0, totalHistoryHeight - listAreaHeight);
        claimsHistoryScrollOffset = Math.min(claimsHistoryScrollOffset, maxHistoryScroll);

        // Draw modern scrollbar if needed
        if (maxHistoryScroll > 0) {
            int scrollbarX = leftMargin + contentWidth - 15; // Position scrollbar inside the new layout

            // Draw scrollbar track - thinner and more subtle
            context.fill(scrollbarX, listAreaY, scrollbarX + 4, listAreaY + listAreaHeight, 0x20FFFFFF);

            // Calculate scrollbar height and position - ensure it's not too small
            int scrollbarHeight = Math.max(40, listAreaHeight * listAreaHeight / (totalHistoryHeight + listAreaHeight));

            // Calculate scrollbar position - ensure it can reach the bottom
            float scrollRatio = (float)claimsHistoryScrollOffset / maxHistoryScroll;
            int scrollbarY = listAreaY + (int)((listAreaHeight - scrollbarHeight) * scrollRatio);

            // Ensure scrollbar doesn't go out of bounds
            scrollbarY = Math.max(listAreaY, Math.min(scrollbarY, listAreaY + listAreaHeight - scrollbarHeight));

            // Draw scrollbar handle - rounded corners effect with more opacity
            drawRoundedRect(context, scrollbarX, scrollbarY, 4, scrollbarHeight, 0xC0FFFFFF);
        }

        // Apply scissor to clip content to visible area
        context.enableScissor(
            leftMargin + 5,
            listAreaY,
            leftMargin + contentWidth - 20,
            listAreaY + listAreaHeight
        );

        // Draw claim history entries with modern card styling
        int entryY = listAreaY - claimsHistoryScrollOffset;
        int cardWidth = contentWidth - 25; // Optimized width for new layout

        if (claimHistory.isEmpty()) {
            // Show empty state message
            int emptyMessageY = listAreaY + listAreaHeight / 2 - 20;
            context.drawCenteredTextWithShadow(this.textRenderer, "No claim history available",
                leftMargin + (contentWidth - 10) / 2, emptyMessageY, 0xAAAAAA);
            context.drawCenteredTextWithShadow(this.textRenderer, "Use the claim tool to create history",
                leftMargin + (contentWidth - 10) / 2, emptyMessageY + 12, 0x888888);
        } else {
            // Draw history entries as modern cards
            for (com.pokecobble.town.claim.SimpleClaimHistoryEntry entry : claimHistory) {
                // Skip if entry is completely outside visible area
                if (entryY + entryCardHeight < listAreaY || entryY > listAreaY + listAreaHeight) {
                    entryY += entryCardHeight + entryCardSpacing;
                    continue;
                }

                // Check if mouse is hovering over this entry card
                boolean isHovered = mouseX >= leftMargin + 5 && mouseX <= leftMargin + 5 + cardWidth &&
                                   mouseY >= entryY && mouseY <= entryY + entryCardHeight &&
                                   mouseY >= listAreaY && mouseY <= listAreaY + listAreaHeight;

                // Draw modern entry card background with glass effect
                int cardColor = isHovered ? 0x60404040 : 0x40303030;
                context.fill(leftMargin + 5, entryY, leftMargin + 5 + cardWidth, entryY + entryCardHeight, cardColor);

                // Draw subtle glass highlight at the top
                context.fill(leftMargin + 5, entryY, leftMargin + 5 + cardWidth, entryY + 1, 0x20FFFFFF);
                context.fill(leftMargin + 5, entryY, leftMargin + 6, entryY + entryCardHeight, 0x20FFFFFF);

                // Draw subtle shadow at the bottom
                context.fill(leftMargin + 5, entryY + entryCardHeight - 1, leftMargin + 5 + cardWidth, entryY + entryCardHeight, 0x20000000);

                // Determine color based on action
                int actionColor;
                switch (entry.getAction()) {
                    case CLAIM:
                        actionColor = 0x55FF55; // Green
                        break;
                    case UNCLAIM:
                        actionColor = 0xFF5555; // Red
                        break;
                    case MODIFY:
                        actionColor = 0xFFAA00; // Orange
                        break;
                    default:
                        actionColor = 0xFFFFFF; // White
                }

                // Draw action text
                String actionText = entry.getActionDescription();
                context.drawTextWithShadow(this.textRenderer, actionText,
                    leftMargin + 10, entryY + (entryCardHeight - 8) / 2, actionColor);

                // Draw time text (right side of card)
                String timeText = entry.getFormattedTimestamp();
                int timeWidth = this.textRenderer.getWidth(timeText);
                context.drawTextWithShadow(this.textRenderer, timeText,
                    leftMargin + 5 + cardWidth - timeWidth - 10, entryY + (entryCardHeight - 8) / 2, 0xAAAAAA);

                entryY += entryCardHeight + entryCardSpacing;
            }
        }

        // Disable scissor
        context.disableScissor();
    }

    /**
     * Checks if town data synchronization appears to be complete.
     * This helps avoid showing incorrect rank data before synchronization finishes.
     *
     * @param town The town to check
     * @return true if synchronization appears complete, false otherwise
     */
    private boolean isTownDataSynchronized(Town town) {
        if (town == null) {
            return false;
        }

        // Check if we have TownPlayer objects or rank data for most players in the town
        List<UUID> townPlayerIds = town.getPlayers();
        if (townPlayerIds.isEmpty()) {
            return true; // Empty town is considered synchronized
        }

        int playersWithData = 0;
        for (UUID playerId : townPlayerIds) {
            TownPlayer townPlayer = town.getPlayer(playerId);
            TownPlayerRank rank = town.getPlayerRank(playerId);
            if (townPlayer != null || rank != null) {
                playersWithData++;
            }
        }

        // Consider synchronized if we have data for at least 80% of players
        double synchronizationRatio = (double) playersWithData / townPlayerIds.size();
        return synchronizationRatio >= 0.8;
    }

    /**
     * Creates a list of players for the town.
     * In multiplayer, uses real player data.
     * In singleplayer, generates dummy players for testing.
     * Uses caching to prevent regeneration on every frame.
     */
    private List<TownPlayer> createDummyPlayerList(Town town) {
        // Always get fresh player ranks during elections
        boolean hasElection = false;
        if (town != null) {
            Election election = ElectionManager.getInstance().getElection(town);
            hasElection = (election != null || town.isInElection());
        }

        // Return cached list if the town hasn't changed and no sort/filter/search options changed
        // But always refresh during elections to get the latest ranks
        // Simplified cache validation - no version checking, cache is cleared on every refresh
        if (!hasElection && town != null && lastTownId != null && town.getId().equals(lastTownId) &&
            cachedPlayerList != null) {
            return cachedPlayerList;
        }

        // Create a new list if cache is invalid
        List<TownPlayer> players = new ArrayList<>();

        if (town == null) {
            return players;
        }

        // Check if we're in a multiplayer environment
        boolean isMultiplayer = client.getNetworkHandler() != null && client.getNetworkHandler().getPlayerList() != null;

        // Check if town data synchronization is complete in multiplayer
        if (isMultiplayer && !isTownDataSynchronized(town)) {
            // In multiplayer, if synchronization isn't complete, return empty list to avoid showing incorrect data
            return players; // Return empty list
        }

        // Get the list of player UUIDs in the town
        List<UUID> townPlayerIds = town.getPlayers();

        if (isMultiplayer) {
            // We're in multiplayer - use real player data

            // Add current player with their actual rank from the town (using authoritative source)
            if (client.player != null && townPlayerIds.contains(client.player.getUuid())) {
                TownPlayerRank currentPlayerRank = town.getPlayerRank(client.player.getUuid());
                if (currentPlayerRank == null) {
                    // For the current player, we should always have rank data
                    // If not, it means synchronization hasn't completed yet
                    com.pokecobble.Pokecobbleclaim.LOGGER.warn("Current player rank not found in town data - synchronization may be incomplete");
                    currentPlayerRank = TownPlayerRank.MEMBER; // Temporary fallback
                }
                players.add(new TownPlayer(client.player.getUuid(), client.player.getName().getString(), currentPlayerRank, true));
            }

            // Add other players from the town data (both online and offline)
            for (UUID playerId : townPlayerIds) {
                // Skip the current player as we've already added them
                if (client.player != null && playerId.equals(client.player.getUuid())) {
                    continue;
                }

                // Get the TownPlayer object which should contain the correct name and data
                TownPlayer townPlayer = town.getPlayer(playerId);
                if (townPlayer != null) {
                    // CRITICAL FIX: Always use the authoritative rank source from town.getPlayerRank()
                    // instead of townPlayer.getRank() to ensure we get the latest rank data
                    TownPlayerRank authoritativeRank = town.getPlayerRank(playerId);
                    if (authoritativeRank != null) {
                        players.add(new TownPlayer(townPlayer.getUuid(), townPlayer.getName(),
                                                 authoritativeRank, townPlayer.isOnline()));
                    } else {
                        // Fallback to TownPlayer rank if authoritative rank is not available
                        players.add(new TownPlayer(townPlayer.getUuid(), townPlayer.getName(),
                                                 townPlayer.getRank(), townPlayer.isOnline()));
                    }
                } else {
                    // Get the actual rank from the town - this should be available even if TownPlayer object isn't
                    TownPlayerRank rank = town.getPlayerRank(playerId);

                    if (rank != null) {
                        // We have rank data, try to get player name
                        net.minecraft.client.network.PlayerListEntry playerEntry = client.getNetworkHandler().getPlayerListEntry(playerId);
                        if (playerEntry != null) {
                            // This is an online player
                            String playerName = playerEntry.getProfile().getName();
                            players.add(new TownPlayer(playerId, playerName, rank, true));
                        } else {
                            // Offline player - use stored name if available, otherwise "Unknown Player"
                            String playerName = town.getPlayerName(playerId);
                            if (playerName.equals("Unknown Player")) {
                                // Try to get name from player data storage
                                try {
                                    com.pokecobble.town.data.PlayerDataStorage.SerializablePlayerData playerData =
                                        com.pokecobble.town.data.PlayerDataStorage.loadPlayerData(playerId);
                                    if (playerData != null && playerData.getPlayerName() != null) {
                                        playerName = playerData.getPlayerName();
                                    }
                                } catch (Exception e) {
                                    // Silently handle errors
                                }
                            }
                            players.add(new TownPlayer(playerId, playerName, rank, false));
                        }
                    } else {
                        // No rank data available - this indicates incomplete synchronization
                        // Skip this player for now rather than showing incorrect data
                        com.pokecobble.Pokecobbleclaim.LOGGER.warn("Player " + playerId + " in town but no rank data available - skipping until synchronization completes");
                    }
                }
            }
        } else {
            // We're in singleplayer - use real player data like in multiplayer

            // Add other players from the town data (both online and offline)
            for (UUID playerId : townPlayerIds) {
                // Skip the current player as we've already added them
                if (client.player != null && playerId.equals(client.player.getUuid())) {
                    continue;
                }

                // Get the TownPlayer object which should contain the correct name and data
                TownPlayer townPlayer = town.getPlayer(playerId);
                if (townPlayer != null) {
                    // Use the TownPlayer data directly - it contains the correct name and online status
                    players.add(new TownPlayer(townPlayer.getUuid(), townPlayer.getName(),
                                             townPlayer.getRank(), townPlayer.isOnline()));
                } else {
                    // Get the actual rank from the town - this should be available
                    TownPlayerRank rank = town.getPlayerRank(playerId);

                    if (rank != null) {
                        // Use stored name if available, otherwise "Unknown Player"
                        String playerName = town.getPlayerName(playerId);
                        if (playerName.equals("Unknown Player")) {
                            // Try to get name from player data storage
                            try {
                                com.pokecobble.town.data.PlayerDataStorage.SerializablePlayerData playerData =
                                    com.pokecobble.town.data.PlayerDataStorage.loadPlayerData(playerId);
                                if (playerData != null && playerData.getPlayerName() != null) {
                                    playerName = playerData.getPlayerName();
                                }
                            } catch (Exception e) {
                                // Silently handle errors
                            }
                        }
                        // In singleplayer, assume players are offline unless they're the current player
                        players.add(new TownPlayer(playerId, playerName, rank, false));
                    } else {
                        // No rank data available - skip this player
                        com.pokecobble.Pokecobbleclaim.LOGGER.warn("Player " + playerId + " in town but no rank data available in singleplayer");
                    }
                }
            }

            // Add current player with their actual rank from the town
            if (client.player != null && townPlayerIds.contains(client.player.getUuid())) {
                TownPlayerRank currentPlayerRank = town.getPlayerRank(client.player.getUuid());
                if (currentPlayerRank == null) {
                    currentPlayerRank = TownPlayerRank.OWNER; // Default to owner in singleplayer if no data
                }
                players.add(new TownPlayer(client.player.getUuid(), client.player.getName().getString(), currentPlayerRank, true));
            }


        }

        // Apply filtering based on current filter type
        List<TownPlayer> filteredPlayers = new ArrayList<>(players);
        if (currentFilterType == FilterType.ONLINE) {
            filteredPlayers.removeIf(player -> !player.isOnline());
        } else if (currentFilterType == FilterType.OFFLINE) {
            filteredPlayers.removeIf(TownPlayer::isOnline);
        }

        // Filter out ADMIN_VIEWER rank from player lists (invisible to normal players)
        filteredPlayers.removeIf(player -> !player.getRank().isVisibleInPlayerList());

        // Apply search if there's a query
        if (!searchQuery.isEmpty()) {
            filteredPlayers.removeIf(player -> !player.getName().toLowerCase().contains(searchQuery.toLowerCase()));
        }

        // Apply sorting based on current sort type
        switch (currentSortType) {
            case RANK:
                // Sort by rank (Owner > Deputy > Council > Resident > Citizen)
                // Use a completely different approach with explicit rank ordering

                // Create separate lists for each rank
                List<TownPlayer> owners = new ArrayList<>();
                List<TownPlayer> deputies = new ArrayList<>();
                List<TownPlayer> council = new ArrayList<>();
                List<TownPlayer> residents = new ArrayList<>();
                List<TownPlayer> citizens = new ArrayList<>();

                // Categorize players by rank
                for (TownPlayer player : filteredPlayers) {
                    switch (player.getRank()) {
                        case OWNER:
                            owners.add(player);
                            break;
                        case ADMIN:
                            deputies.add(player);
                            break;
                        case MODERATOR:
                            council.add(player);
                            break;
                        case MEMBER:
                            residents.add(player);
                            break;
                        case VISITOR:
                            citizens.add(player);
                            break;
                        case ADMIN_VIEWER:
                            // Admin viewers are treated as owners for sorting but filtered out from display
                            owners.add(player);
                            break;
                    }
                }

                // Sort each list by name
                Comparator<TownPlayer> nameComparator = Comparator.comparing(TownPlayer::getName, String::compareToIgnoreCase);
                Collections.sort(owners, nameComparator);
                Collections.sort(deputies, nameComparator);
                Collections.sort(council, nameComparator);
                Collections.sort(residents, nameComparator);
                Collections.sort(citizens, nameComparator);

                // Clear the original list and add players in order of rank
                filteredPlayers.clear();
                filteredPlayers.addAll(owners);
                filteredPlayers.addAll(deputies);
                filteredPlayers.addAll(council);
                filteredPlayers.addAll(residents);
                filteredPlayers.addAll(citizens);
                break;
            case NAME:
                // Sort by name alphabetically
                filteredPlayers.sort((p1, p2) -> p1.getName().compareToIgnoreCase(p2.getName()));
                break;
            case NEWEST:
                // For demo purposes, we'll use the UUID's least significant bits as a proxy for "join time"
                filteredPlayers.sort((p1, p2) -> Long.compare(p2.getUuid().getLeastSignificantBits(), p1.getUuid().getLeastSignificantBits()));
                break;
            case OLDEST:
                // For demo purposes, we'll use the UUID's least significant bits as a proxy for "join time"
                filteredPlayers.sort((p1, p2) -> Long.compare(p1.getUuid().getLeastSignificantBits(), p2.getUuid().getLeastSignificantBits()));
                break;
        }

        // Update cache with the filtered and sorted list
        if (town != null) {
            lastTownId = town.getId();
            cachedPlayerList = new ArrayList<>(filteredPlayers); // Cache the current list
        }

        return filteredPlayers;
    }

    /**
     * Renders the Level subcategory content.
     */
    private void renderLevelSubcategory(DrawContext context, int mouseX, int mouseY, Town playerTown, int contentX, int contentY, int contentWidth, int contentHeight) {
        // Layout variables
        int leftMargin = contentX + 15;
        int rowHeight = 12; // Even more compact row height
        int sectionSpacing = 6; // Further reduced spacing between sections
        int currentY = contentY + 8; // Start even closer to the top

        // Get town level (for demo purposes)
        int townLevel = 3; // Example level
        int currentExp = 750; // Example current experience
        int expForNextLevel = 1000; // Example experience needed for next level

        // Draw level title and progress on the same line
        context.drawTextWithShadow(this.textRenderer, Text.literal("Town Level").formatted(Formatting.BOLD),
            leftMargin, currentY, 0xFFFFFF);

        // Draw current level and progress percentage on the same line
        String levelText = "Lvl " + townLevel + " (" + (currentExp * 100 / expForNextLevel) + "%)";
        int levelTextWidth = this.textRenderer.getWidth(levelText);
        context.drawTextWithShadow(this.textRenderer, levelText,
            leftMargin + contentWidth - 60 - levelTextWidth, currentY, 0xFFFFFF);
        currentY += rowHeight + 2;

        // Draw progress bar background
        int barWidth = contentWidth - 60;
        int barHeight = 6; // Even smaller height
        int barX = leftMargin;
        int barY = currentY;
        context.fill(barX, barY, barX + barWidth, barY + barHeight, 0x80000000);

        // Draw progress bar fill
        int fillWidth = (int)((float)currentExp / expForNextLevel * barWidth);
        context.fill(barX, barY, barX + fillWidth, barY + barHeight, 0xFFFFAA00);

        // Draw progress bar border
        context.drawBorder(barX, barY, barWidth, barHeight, 0xFFFFFFFF);
        currentY += barHeight + 6; // Further reduced spacing

        // Draw next level benefits section with title on same line as first benefit
        int nextLevel = townLevel + 1;

        // Show only the next level benefits
        String[][] allBenefits = {
            {"Basic town features", "Town chat", "Town spawn point"},
            {"Custom town banner", "Town welcome message", "Town shop discounts"},
            {"Town teleport point", "Town protection", "Town storage"},
            {"Custom building styles", "Town weather control", "Town farm boost"},
            {"Town aura effects", "Town flight", "Town special events"}
        };

        // Check if next level exists
        if (nextLevel <= 5) {
            String[] nextLevelBenefits = allBenefits[nextLevel - 1];

            // Draw next level title and first benefit on the same line
            context.drawTextWithShadow(this.textRenderer, Text.literal("Next Level:").formatted(Formatting.BOLD),
                leftMargin, currentY, 0xFFFFFF);
            context.drawTextWithShadow(this.textRenderer, "Lvl " + nextLevel,
                leftMargin + 70, currentY, 0xFFFFAA00);
            currentY += rowHeight;

            // Draw the 3 benefits in a more compact way
            int benefitX = leftMargin + 10;
            for (int i = 0; i < nextLevelBenefits.length; i++) {
                context.drawTextWithShadow(this.textRenderer, "• " + nextLevelBenefits[i],
                    benefitX, currentY, 0xFFFFFF);
                currentY += rowHeight - 1; // Slightly overlap rows
            }
        } else {
            // Max level reached
            context.drawTextWithShadow(this.textRenderer, Text.literal("Next Level:").formatted(Formatting.BOLD),
                leftMargin, currentY, 0xFFFFFF);
            context.drawTextWithShadow(this.textRenderer, "Maximum level reached!",
                leftMargin + 70, currentY, 0x55FF55);
            currentY += rowHeight;
        }

        // Add "See All Benefits" button - smaller and more compact
        int seeAllButtonWidth = 90;
        int seeAllButtonHeight = 14;
        int seeAllButtonX = leftMargin;
        int seeAllButtonY = currentY + 1;
        boolean seeAllHovered = mouseX >= seeAllButtonX && mouseX <= seeAllButtonX + seeAllButtonWidth &&
                               mouseY >= seeAllButtonY && mouseY <= seeAllButtonY + seeAllButtonHeight;
        drawModernButton(context, seeAllButtonX, seeAllButtonY, seeAllButtonWidth, seeAllButtonHeight, 0xFF2196F3, seeAllHovered, true);
        context.drawCenteredTextWithShadow(this.textRenderer, "See All Benefits",
            seeAllButtonX + seeAllButtonWidth / 2, seeAllButtonY + 3, 0xFFFFFF);

        currentY += seeAllButtonHeight + sectionSpacing - 2; // Further reduced spacing

        // Add "Contribute" button
        int contributeButtonWidth = 100;
        int contributeButtonHeight = 16;
        int contributeButtonX = leftMargin;
        int contributeButtonY = currentY;
        boolean contributeHovered = mouseX >= contributeButtonX && mouseX <= contributeButtonX + contributeButtonWidth &&
                                   mouseY >= contributeButtonY && mouseY <= contributeButtonY + contributeButtonHeight;
        drawModernButton(context, contributeButtonX, contributeButtonY, contributeButtonWidth, contributeButtonHeight, 0xFF9C27B0, contributeHovered, true);
        context.drawCenteredTextWithShadow(this.textRenderer, "Contribute Coins",
            contributeButtonX + contributeButtonWidth / 2, contributeButtonY + 4, 0xFFFFFF);

        currentY += contributeButtonHeight + 6; // Reduced spacing

        // Draw top contributors section - even more compact
        currentY += 5; // Small spacing

        // Draw top contributors title and first contributor on the same line
        context.drawTextWithShadow(this.textRenderer, Text.literal("Top:").formatted(Formatting.BOLD),
            leftMargin, currentY + 1, 0xFFFFFF);

        // Draw first contributor on the same line as the title
        context.drawTextWithShadow(this.textRenderer, "1. Player1: 5000",
            leftMargin + 35, currentY + 1, 0xFFFFFF);
        currentY += rowHeight - 1; // Slightly overlap rows

        // Calculate positions for compact layout
        int column1X = leftMargin + 5;
        int column2X = leftMargin + contentWidth/2 - 20;

        // Draw remaining contributors in a compact grid (2x2)
        String[][] contributors = {
            {"2. Player2: 3500", "4. You: 1500"},
            {"3. Player3: 2000", "5. Player5: 1000"}
        };

        for (int row = 0; row < 2; row++) {
            for (int col = 0; col < 2; col++) {
                int color = (row == 1 && col == 1) ? 0xFFFFFF : (row == 0 && col == 1) ? 0xFFFF55 : 0xFFFFFF;
                int x = (col == 0) ? column1X : column2X;
                context.drawTextWithShadow(this.textRenderer, contributors[row][col],
                    x, currentY + (row * (rowHeight - 1)), color);
            }
        }
    }

    /**
     * Renders the Bank subcategory content with modern design.
     */
    private void renderBankSubcategory(DrawContext context, int mouseX, int mouseY, Town playerTown, int contentX, int contentY, int contentWidth, int contentHeight) {
        // Modern layout with integrated header (matching claims subcategory exactly)
        int leftMargin = contentX + 5; // Reduced margin for more space
        int headerHeight = 32; // Taller header for better button integration
        int currentY = contentY + 5; // Start closer to top

        // Update button hover states and animations
        updateBankButtonAnimations();

        // Header controls with expandable animations (matching claims design exactly - no header background)
        int headerPadding = 8;
        int controlY = currentY + (headerHeight - 16) / 2; // Center buttons vertically in header
        int controlSpacing = 8;
        int currentX = leftMargin + headerPadding;

        // Deposit button (💰 icon, green color)
        depositButtonHovered = isMouseOverExpandableButton(mouseX, mouseY, currentX, controlY, depositButtonAnimation, "💰", "Deposit");
        currentX = drawExpandableButton(context, currentX, controlY, "💰", "Deposit", 0xFF4CAF50, depositButtonAnimation, depositButtonHovered);
        currentX += controlSpacing;

        // Withdraw button (💸 icon, red color)
        withdrawButtonHovered = isMouseOverExpandableButton(mouseX, mouseY, currentX, controlY, withdrawButtonAnimation, "💸", "Withdraw");
        currentX = drawExpandableButton(context, currentX, controlY, "💸", "Withdraw", 0xFFE53935, withdrawButtonAnimation, withdrawButtonHovered);
        currentX += controlSpacing;

        // Bank App button (🏦 icon, blue color)
        bankAppButtonHovered = isMouseOverExpandableButton(mouseX, mouseY, currentX, controlY, bankAppButtonAnimation, "🏦", "Bank App");
        currentX = drawExpandableButton(context, currentX, controlY, "🏦", "Bank App", 0xFF2196F3, bankAppButtonAnimation, bankAppButtonHovered);
        currentX += controlSpacing;

        // Time mode dropdown button with expandable animation (📊 icon, purple color) - positioned beside Bank App
        timeDropdownButtonHovered = isMouseOverExpandableButton(mouseX, mouseY, currentX, controlY, timeDropdownButtonAnimation, "📊", getCurrentTimeModeLabel());
        currentX = drawExpandableButton(context, currentX, controlY, "📊", getCurrentTimeModeLabel(), 0xFF9C27B0, timeDropdownButtonAnimation, timeDropdownButtonHovered);

        // Bank balance positioned at the full right of the header - fixed position
        int balanceCardWidth = 120; // Slightly larger for better visibility
        int headerRightMargin = leftMargin + contentWidth - 10; // Right edge of header
        int balanceX = headerRightMargin - balanceCardWidth; // Position at full right
        drawBankBalance(context, balanceX, controlY, balanceCardWidth);

        currentY += headerHeight + 10;

        // Money graph area - positioned after header with minimal spacing
        int graphAreaY = currentY; // Start right after header since balance is now inline
        int availableHeight = contentY + contentHeight - graphAreaY - 5; // Reduced bottom margin from 10 to 5
        int graphAreaHeight = Math.max(120, availableHeight); // Reduced minimum from 150 to 120 for more space

        // Ensure graph fits within content bounds with minimal margin
        if (graphAreaY + graphAreaHeight > contentY + contentHeight - 5) {
            graphAreaHeight = contentY + contentHeight - graphAreaY - 5;
        }

        // Render the enhanced interactive money graph - use maximum space with no margins
        renderMoneyGraph(context, mouseX, mouseY, playerTown, leftMargin, graphAreaY, contentWidth, graphAreaHeight);
    }

    /**
     * Renders an enhanced interactive money graph with time modes and drag functionality.
     */
    private void renderMoneyGraph(DrawContext context, int mouseX, int mouseY, Town playerTown, int graphX, int graphY, int graphWidth, int graphHeight) {
        // Draw enhanced graph background with gradient
        context.fill(graphX, graphY, graphX + graphWidth, graphY + graphHeight, 0x40000000);
        context.fill(graphX, graphY, graphX + graphWidth, graphY + 2, 0x60FFFFFF);
        context.fill(graphX, graphY, graphX + 2, graphY + graphHeight, 0x60FFFFFF);
        context.fill(graphX + graphWidth - 2, graphY, graphX + graphWidth, graphY + graphHeight, 0x30FFFFFF);
        context.fill(graphX, graphY + graphHeight - 2, graphX + graphWidth, graphY + graphHeight, 0x30FFFFFF);

        // No padding for maximum graph space
        int padding = 0; // Removed all padding
        int headerHeight = 20; // Further reduced for more graph space

        // Ensure minimum dimensions and bounds checking
        if (graphWidth < 200 || graphHeight < 100) return;

        int graphContentX = graphX + padding;
        int graphContentY = graphY + padding; // Header is now outside, so no header height offset
        int graphContentWidth = graphWidth - (padding * 2);
        int graphContentHeight = graphHeight - (padding * 2); // No header height reduction

        // Time mode controls now integrated in header - no separate rendering needed

        // Get data based on current time mode
        MoneyGraphData[] graphData = getGraphDataForTimeMode(currentTimeMode);

        // Apply scroll offset to data
        int visibleDataPoints = Math.min(20, graphData.length); // Show max 20 points at once
        int startIndex = Math.max(0, Math.min(graphScrollOffset, graphData.length - visibleDataPoints));
        int endIndex = Math.min(graphData.length, startIndex + visibleDataPoints);

        // Update max scroll offset
        maxScrollOffset = Math.max(0, graphData.length - visibleDataPoints);

        // Calculate chart area with maximum space usage - no margins
        int chartY = graphContentY;
        int chartHeight = Math.max(50, graphContentHeight - 5); // Minimal bottom margin for scroll indicator
        int chartWidth = Math.max(100, graphContentWidth); // Use full width
        int chartX = graphContentX; // No left offset

        // Clamp chart area to ensure it doesn't exceed graph bounds
        chartWidth = Math.min(chartWidth, graphX + graphWidth - chartX - padding);
        chartHeight = Math.min(chartHeight, graphY + graphHeight - chartY - 20);

        // Find min and max values for visible data
        int minValue = Integer.MAX_VALUE;
        int maxValue = Integer.MIN_VALUE;
        int previousValue = startIndex > 0 ? graphData[startIndex - 1].amount : 0;

        for (int i = startIndex; i < endIndex; i++) {
            minValue = Math.min(minValue, graphData[i].amount);
            maxValue = Math.max(maxValue, graphData[i].amount);
        }

        // Add padding to the range for better visualization
        int range = maxValue - minValue;
        if (range == 0) range = 100; // Prevent division by zero
        minValue -= range * 0.15;
        maxValue += range * 0.15;

        // Draw enhanced grid with better styling
        renderGraphGrid(context, chartX, chartY, chartWidth, chartHeight, minValue, maxValue);

        // Draw the enhanced line graph with colors
        renderGraphLines(context, graphData, startIndex, endIndex, chartX, chartY, chartWidth, chartHeight, minValue, maxValue, previousValue);

        // Draw enhanced data points with hover effects
        String hoveredInfo = renderGraphPoints(context, mouseX, mouseY, graphData, startIndex, endIndex, chartX, chartY, chartWidth, chartHeight, minValue, maxValue);

        // Draw enhanced tooltip
        if (hoveredInfo != null) {
            renderEnhancedTooltip(context, mouseX, mouseY, hoveredInfo, graphX, graphY, graphWidth, graphHeight);
        }

        // Draw scroll indicator if needed - positioned at very bottom
        if (maxScrollOffset > 0) {
            renderScrollIndicator(context, graphX, graphY + graphHeight - 6, graphWidth, 5);
        }
    }

    /**
     * Renders the graph header with time mode controls only.
     */
    private void renderGraphHeader(DrawContext context, int mouseX, int mouseY, int headerX, int headerY, int headerWidth, int headerHeight) {
        // Time mode buttons - positioned at the top left, made smaller
        String[] modes = {"Hour", "Day", "Month", "Year"};
        TimeMode[] timeModes = {TimeMode.HOUR, TimeMode.DAY, TimeMode.MONTH, TimeMode.YEAR};
        int buttonWidth = 35; // Reduced from 45
        int buttonHeight = 14; // Reduced from 16
        int buttonSpacing = 3; // Reduced from 4
        int buttonsStartX = headerX;

        for (int i = 0; i < modes.length; i++) {
            int buttonX = buttonsStartX + i * (buttonWidth + buttonSpacing);
            int buttonY = headerY + 2;
            boolean isSelected = currentTimeMode == timeModes[i];
            boolean isHovered = mouseX >= buttonX && mouseX <= buttonX + buttonWidth &&
                               mouseY >= buttonY && mouseY <= buttonY + buttonHeight;

            // Draw button background
            int bgColor = isSelected ? 0x804CAF50 : (isHovered ? 0x40FFFFFF : 0x20FFFFFF);
            context.fill(buttonX, buttonY, buttonX + buttonWidth, buttonY + buttonHeight, bgColor);

            // Draw button border
            int borderColor = isSelected ? 0xFF4CAF50 : 0x40FFFFFF;
            context.fill(buttonX, buttonY, buttonX + buttonWidth, buttonY + 1, borderColor);
            context.fill(buttonX, buttonY, buttonX + 1, buttonY + buttonHeight, borderColor);
            context.fill(buttonX + buttonWidth - 1, buttonY, buttonX + buttonWidth, buttonY + buttonHeight, borderColor);
            context.fill(buttonX, buttonY + buttonHeight - 1, buttonX + buttonWidth, buttonY + buttonHeight, borderColor);

            // Draw button text
            int textColor = isSelected ? 0xFFFFFF : 0xCCCCCC;
            int textX = buttonX + (buttonWidth - this.textRenderer.getWidth(modes[i])) / 2;
            context.drawTextWithShadow(this.textRenderer, modes[i], textX, buttonY + 5, textColor);
        }

        // Removed drag to scroll text
    }

    /**
     * Gets graph data based on the current time mode with caching to avoid regenerating every frame.
     */
    private MoneyGraphData[] getGraphDataForTimeMode(TimeMode mode) {
        // Use cached data to avoid regenerating every frame
        switch (mode) {
            case HOUR:
                if (cachedHourlyData == null) {
                    cachedHourlyData = generateHourlyData();
                }
                return cachedHourlyData;
            case DAY:
                if (cachedDailyData == null) {
                    cachedDailyData = generateDailyData();
                }
                return cachedDailyData;
            case MONTH:
                if (cachedMonthlyData == null) {
                    cachedMonthlyData = generateMonthlyData();
                }
                return cachedMonthlyData;
            case YEAR:
                if (cachedYearlyData == null) {
                    cachedYearlyData = generateYearlyData();
                }
                return cachedYearlyData;
            default:
                if (cachedDailyData == null) {
                    cachedDailyData = generateDailyData();
                }
                return cachedDailyData;
        }
    }

    /**
     * Generates sample hourly data.
     */
    private MoneyGraphData[] generateHourlyData() {
        MoneyGraphData[] data = new MoneyGraphData[24];
        int baseAmount = 1000;
        for (int i = 0; i < 24; i++) {
            int change = (int)(Math.sin(i * 0.3) * 100 + Math.random() * 50 - 25);
            baseAmount += change;
            String time = String.format("%02d:00", i);
            String desc = change > 0 ? "+" + change + " income" : change + " expense";
            String player = change > 0 ? "AutoIncome" : "AutoExpense";
            data[i] = new MoneyGraphData(time, baseAmount, desc, player, change);
        }
        return data;
    }

    /**
     * Generates sample daily data.
     */
    private MoneyGraphData[] generateDailyData() {
        MoneyGraphData[] data = new MoneyGraphData[30];
        int baseAmount = 1000;
        String[] players = {"Player1", "Player2", "Player3", "Player4", "Player5", "TownBank"};

        for (int i = 0; i < 30; i++) {
            int change = (int)(Math.random() * 600 - 200); // -200 to +400
            baseAmount += change;
            String day = "Day " + (i + 1);
            String desc = change > 0 ? "+" + change + " deposit" : Math.abs(change) + " withdrawal";
            String player = players[(int)(Math.random() * players.length)];
            data[i] = new MoneyGraphData(day, baseAmount, desc, player, change);
        }
        return data;
    }

    /**
     * Generates sample monthly data.
     */
    private MoneyGraphData[] generateMonthlyData() {
        String[] months = {"Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"};
        MoneyGraphData[] data = new MoneyGraphData[12];
        int baseAmount = 5000;

        for (int i = 0; i < 12; i++) {
            int change = (int)(Math.random() * 2000 - 500); // -500 to +1500
            baseAmount += change;
            String desc = change > 0 ? "+" + change + " monthly profit" : Math.abs(change) + " monthly loss";
            data[i] = new MoneyGraphData(months[i], baseAmount, desc, "Monthly Summary", change);
        }
        return data;
    }

    /**
     * Generates sample yearly data.
     */
    private MoneyGraphData[] generateYearlyData() {
        MoneyGraphData[] data = new MoneyGraphData[5];
        int baseAmount = 10000;

        for (int i = 0; i < 5; i++) {
            int change = (int)(Math.random() * 5000 - 1000); // -1000 to +4000
            baseAmount += change;
            String year = String.valueOf(2020 + i);
            String desc = change > 0 ? "+" + change + " yearly growth" : Math.abs(change) + " yearly decline";
            data[i] = new MoneyGraphData(year, baseAmount, desc, "Annual Summary", change);
        }
        return data;
    }

    /**
     * Enhanced helper class for money graph data points.
     */
    private static class MoneyGraphData {
        final String label;
        final int amount;
        final String description;
        final String playerName;
        final int change; // The change amount (positive or negative)

        MoneyGraphData(String label, int amount, String description, String playerName, int change) {
            this.label = label;
            this.amount = amount;
            this.description = description;
            this.playerName = playerName;
            this.change = change;
        }

        // Legacy constructor for backward compatibility
        MoneyGraphData(String label, int amount, String description, String playerName) {
            this(label, amount, description, playerName, 0);
        }
    }

    /**
     * Renders the graph grid with enhanced styling.
     */
    private void renderGraphGrid(DrawContext context, int chartX, int chartY, int chartWidth, int chartHeight, int minValue, int maxValue) {
        int gridLines = 6;

        // Draw horizontal grid lines
        for (int i = 0; i <= gridLines; i++) {
            int gridY = chartY + (chartHeight * i / gridLines);

            // Alternate grid line opacity
            int opacity = (i % 2 == 0) ? 0x30FFFFFF : 0x20FFFFFF;
            context.fill(chartX, gridY, chartX + chartWidth, gridY + 1, opacity);

            // Removed left side money amount text
        }

        // Draw vertical grid lines for time markers
        int timeMarkers = 5;
        for (int i = 0; i <= timeMarkers; i++) {
            int gridX = chartX + (chartWidth * i / timeMarkers);
            context.fill(gridX, chartY, gridX + 1, chartY + chartHeight, 0x20FFFFFF);
        }
    }

    /**
     * Renders the graph lines with green/red colors based on trends.
     */
    private void renderGraphLines(DrawContext context, MoneyGraphData[] graphData, int startIndex, int endIndex,
                                 int chartX, int chartY, int chartWidth, int chartHeight, int minValue, int maxValue, int previousValue) {
        if (endIndex - startIndex <= 1) return;

        for (int i = startIndex; i < endIndex - 1; i++) {
            // Calculate positions
            int dataIndex1 = i - startIndex;
            int dataIndex2 = dataIndex1 + 1;
            int visiblePoints = endIndex - startIndex;

            int x1 = chartX + (chartWidth * dataIndex1 / (visiblePoints - 1));
            int x2 = chartX + (chartWidth * dataIndex2 / (visiblePoints - 1));

            int y1 = chartY + chartHeight - (int)((float)(graphData[i].amount - minValue) / (maxValue - minValue) * chartHeight);
            int y2 = chartY + chartHeight - (int)((float)(graphData[i + 1].amount - minValue) / (maxValue - minValue) * chartHeight);

            // Determine line color based on trend
            boolean isIncreasing = graphData[i + 1].amount > graphData[i].amount;
            int lineColor = isIncreasing ? 0xFF4CAF50 : 0xFFE53E3E; // Green for up, red for down

            // Draw thicker line with gradient effect
            drawEnhancedLine(context, x1, y1, x2, y2, lineColor, 3);
        }
    }

    /**
     * Renders graph points with enhanced hover effects.
     */
    private String renderGraphPoints(DrawContext context, int mouseX, int mouseY, MoneyGraphData[] graphData,
                                   int startIndex, int endIndex, int chartX, int chartY, int chartWidth, int chartHeight,
                                   int minValue, int maxValue) {
        String hoveredInfo = null;
        int visiblePoints = endIndex - startIndex;

        for (int i = startIndex; i < endIndex; i++) {
            int dataIndex = i - startIndex;
            int pointX = chartX + (chartWidth * dataIndex / (visiblePoints - 1));
            int pointY = chartY + chartHeight - (int)((float)(graphData[i].amount - minValue) / (maxValue - minValue) * chartHeight);

            // Check if mouse is hovering over this point
            boolean isHovered = Math.abs(mouseX - pointX) <= 10 && Math.abs(mouseY - pointY) <= 10;

            // Determine point color based on change
            int pointColor;
            if (isHovered) {
                pointColor = 0xFFFFD700; // Gold when hovered
            } else if (graphData[i].change > 0) {
                pointColor = 0xFF4CAF50; // Green for positive
            } else if (graphData[i].change < 0) {
                pointColor = 0xFFE53E3E; // Red for negative
            } else {
                pointColor = 0xFF9E9E9E; // Gray for neutral
            }

            // Draw enhanced point with glow effect
            int pointSize = isHovered ? 8 : 5;

            // Draw glow effect
            if (isHovered) {
                context.fill(pointX - pointSize - 2, pointY - pointSize - 2,
                           pointX + pointSize + 2, pointY + pointSize + 2, 0x40FFD700);
            }

            // Draw main point
            context.fill(pointX - pointSize/2, pointY - pointSize/2,
                       pointX + pointSize/2, pointY + pointSize/2, pointColor);

            // Draw inner highlight
            context.fill(pointX - pointSize/4, pointY - pointSize/4,
                       pointX + pointSize/4, pointY + pointSize/4, 0x80FFFFFF);

            // Store hover info
            if (isHovered) {
                String changeText = graphData[i].change > 0 ?
                    "+" + formatCurrency(graphData[i].change) :
                    formatCurrency(graphData[i].change);
                hoveredInfo = graphData[i].label + ": " + formatCurrency(graphData[i].amount) +
                             "\n" + changeText + " - " + graphData[i].description +
                             "\nBy: " + graphData[i].playerName;
            }
        }

        return hoveredInfo;
    }

    /**
     * Renders an enhanced tooltip with better styling.
     */
    private void renderEnhancedTooltip(DrawContext context, int mouseX, int mouseY, String hoveredInfo,
                                     int graphX, int graphY, int graphWidth, int graphHeight) {
        String[] lines = hoveredInfo.split("\n");
        int tooltipWidth = 0;
        for (String line : lines) {
            tooltipWidth = Math.max(tooltipWidth, this.textRenderer.getWidth(line));
        }
        tooltipWidth += 20; // More padding
        int tooltipHeight = lines.length * 14 + 12; // Larger line height

        // Position tooltip with better bounds checking
        int tooltipX = mouseX + 15;
        int tooltipY = mouseY - tooltipHeight - 15;
        if (tooltipX + tooltipWidth > graphX + graphWidth - 10) {
            tooltipX = mouseX - tooltipWidth - 15;
        }
        if (tooltipY < graphY + 10) {
            tooltipY = mouseY + 15;
        }

        // Draw enhanced tooltip background with gradient
        context.fill(tooltipX, tooltipY, tooltipX + tooltipWidth, tooltipY + tooltipHeight, 0xF0000000);
        context.fill(tooltipX, tooltipY, tooltipX + tooltipWidth, tooltipY + 2, 0x80FFFFFF);
        context.fill(tooltipX, tooltipY, tooltipX + 2, tooltipY + tooltipHeight, 0x80FFFFFF);
        context.fill(tooltipX + tooltipWidth - 2, tooltipY, tooltipX + tooltipWidth, tooltipY + tooltipHeight, 0x40000000);
        context.fill(tooltipX, tooltipY + tooltipHeight - 2, tooltipX + tooltipWidth, tooltipY + tooltipHeight, 0x40000000);

        // Draw tooltip text with enhanced colors
        for (int i = 0; i < lines.length; i++) {
            int textColor;
            if (i == 0) textColor = 0xFFFFFF; // White for title
            else if (i == 1) {
                // Color based on change amount
                if (lines[i].startsWith("+")) textColor = 0xFF4CAF50; // Green for positive
                else if (lines[i].startsWith("-")) textColor = 0xFFE53E3E; // Red for negative
                else textColor = 0xFFD700; // Gold for neutral
            } else textColor = 0xCCCCCC; // Light gray for details

            context.drawTextWithShadow(this.textRenderer, lines[i],
                tooltipX + 10, tooltipY + 6 + (i * 14), textColor);
        }
    }

    /**
     * Renders scroll indicator at the bottom of the graph - smaller and without text.
     */
    private void renderScrollIndicator(DrawContext context, int x, int y, int width, int height) {
        if (maxScrollOffset <= 0) return;

        // Draw scroll track - smaller margins
        context.fill(x + 10, y, x + width - 10, y + height, 0x40FFFFFF);

        // Calculate scroll thumb position and size
        int trackWidth = width - 20; // Reduced from 40
        int thumbWidth = Math.max(15, trackWidth / (maxScrollOffset + 20)); // Reduced minimum from 20
        int thumbX = x + 10 + (int)((float)graphScrollOffset / maxScrollOffset * (trackWidth - thumbWidth));

        // Draw scroll thumb
        context.fill(thumbX, y + 1, thumbX + thumbWidth, y + height - 1, 0x80FFFFFF);

        // Removed scroll position text
    }

    /**
     * Draws an enhanced line with anti-aliasing effect.
     */
    private void drawEnhancedLine(DrawContext context, int x1, int y1, int x2, int y2, int color, int thickness) {
        // Draw main line
        drawLine(context, x1, y1, x2, y2, color, thickness);

        // Add subtle glow effect
        int glowColor = (color & 0x00FFFFFF) | 0x40000000; // Same color but with transparency
        drawLine(context, x1, y1, x2, y2, glowColor, thickness + 2);
    }

    /**
     * Formats currency values with appropriate suffixes.
     */
    private String formatCurrency(int amount) {
        if (Math.abs(amount) >= 1000000) {
            return String.format("$%.1fM", amount / 1000000.0);
        } else if (Math.abs(amount) >= 1000) {
            return String.format("$%.1fK", amount / 1000.0);
        } else {
            return "$" + amount;
        }
    }

    /**
     * Draws a line between two points with specified thickness.
     */
    private void drawLine(DrawContext context, int x1, int y1, int x2, int y2, int color, int thickness) {
        // Simple line drawing - for a more sophisticated implementation, you might want to use proper line algorithms
        int dx = Math.abs(x2 - x1);
        int dy = Math.abs(y2 - y1);
        int steps = Math.max(dx, dy);

        if (steps == 0) return;

        float xStep = (float)(x2 - x1) / steps;
        float yStep = (float)(y2 - y1) / steps;

        for (int i = 0; i <= steps; i++) {
            int x = x1 + Math.round(xStep * i);
            int y = y1 + Math.round(yStep * i);

            // Draw a small rectangle for thickness
            context.fill(x - thickness/2, y - thickness/2, x + thickness/2, y + thickness/2, color);
        }
    }

    /**
     * Renders the Election subcategory content.
     */
    private void renderElectionSubcategory(DrawContext context, int mouseX, int mouseY, Town playerTown, int contentX, int contentY, int contentWidth, int contentHeight) {
        // Get the election from client-side manager
        Election election = com.pokecobble.town.client.ClientElectionManager.getInstance().getElection(playerTown.getId());
        if (election == null) {
            // No election in progress
            context.drawCenteredTextWithShadow(this.textRenderer,
                    Text.literal("No election is currently in progress.").formatted(Formatting.ITALIC),
                    contentX + contentWidth / 2, contentY + contentHeight / 2, 0xFFFFFF);
            return;
        }

        // Layout variables
        int leftMargin = contentX + 15;
        int rowHeight = 18; // Same as players subcategory
        int currentY = contentY + 10; // Same as players subcategory

        // Draw election title and timer
        context.drawTextWithShadow(this.textRenderer, Text.literal("Mayoral Election").formatted(Formatting.BOLD),
            leftMargin, currentY, 0xFFFFFF);

        // Draw election timer on the right
        long remainingTime = election.getRemainingTime();
        long hours = remainingTime / (60 * 60 * 1000);
        long minutes = (remainingTime % (60 * 60 * 1000)) / (60 * 1000);
        String timeText = "Time remaining: " + hours + "h " + minutes + "m";
        int timeWidth = this.textRenderer.getWidth(timeText);
        context.drawTextWithShadow(this.textRenderer,
                Text.literal(timeText).formatted(Formatting.GOLD),
                contentX + contentWidth - 15 - timeWidth, currentY, 0xFFFFFF);
        currentY += rowHeight;

        // Draw column headers - similar to players subcategory but with votes instead of status
        int rankWidth = 50; // Same as players subcategory
        int nameWidth = 130; // Same as players subcategory
        int votesWidth = 45; // Instead of status
        int actionWidth = 45; // Same as players subcategory

        // Draw column headers
        context.drawTextWithShadow(this.textRenderer, Text.literal("Rank").formatted(Formatting.BOLD),
            leftMargin + 5, currentY, 0xAAAAAA);
        context.drawTextWithShadow(this.textRenderer, Text.literal("Name").formatted(Formatting.BOLD),
            leftMargin + rankWidth + 5, currentY, 0xAAAAAA);
        context.drawTextWithShadow(this.textRenderer, Text.literal("Votes").formatted(Formatting.BOLD),
            leftMargin + rankWidth + nameWidth + 5, currentY, 0xAAAAAA);
        context.drawTextWithShadow(this.textRenderer, Text.literal("Action").formatted(Formatting.BOLD),
            leftMargin + rankWidth + nameWidth + votesWidth + 5, currentY, 0xAAAAAA);

        // Move currentY down to account for the header row
        currentY += rowHeight;

        // Calculate list area dimensions
        int listAreaY = currentY;
        int listAreaHeight = contentHeight - 20 - rowHeight - rowHeight; // Adjust height to account for title/timer and column headers

        // Draw list area background
        context.fill(leftMargin, listAreaY, leftMargin + contentWidth - 30, listAreaY + listAreaHeight, 0x20000000);

        // Get candidates and vote counts
        List<UUID> sortedCandidates = election.getSortedCandidates();
        Map<UUID, Integer> voteCount = election.getVoteCount();

        // Check if player has already voted
        boolean hasVoted = false;
        UUID votedFor = null;

        if (client.player != null) {
            UUID playerId = client.player.getUuid();
            hasVoted = election.hasVoted(playerId);
            votedFor = election.getVotes().get(playerId);
        }

        // Create a list of TownPlayer objects for the candidates
        List<TownPlayer> candidates = new ArrayList<>();
        for (UUID candidateId : sortedCandidates) {
            // Get player name
            String playerName = getPlayerName(candidateId);

            // Get player rank
            TownPlayerRank rank = playerTown.getPlayerRank(candidateId);
            if (rank == null) {
                rank = TownPlayerRank.MEMBER; // Default to member if rank not found
            }

            // Create TownPlayer object
            TownPlayer candidate = new TownPlayer(candidateId, playerName, rank, true);
            candidates.add(candidate);
        }

        // Calculate total height of all candidates
        int totalHeight = candidates.size() * rowHeight;

        // Calculate max scroll
        int maxScroll = Math.max(0, totalHeight - listAreaHeight);
        electionScrollOffset = Math.min(electionScrollOffset, maxScroll);

        // Draw scrollbar if needed - modern style from ModernTownScreen
        if (maxScroll > 0) {
            // Draw scrollbar track - thinner and more subtle
            context.fill(leftMargin + contentWidth - 8, listAreaY, leftMargin + contentWidth - 4, listAreaY + listAreaHeight, 0x20FFFFFF);

            // Calculate scrollbar height and position - ensure it's not too small
            int scrollbarHeight = Math.max(40, listAreaHeight * listAreaHeight / (totalHeight + listAreaHeight));

            // Calculate scrollbar position - ensure it can reach the bottom
            float scrollRatio = (float)electionScrollOffset / maxScroll;
            int scrollbarY = listAreaY + (int)((listAreaHeight - scrollbarHeight) * scrollRatio);

            // Ensure scrollbar doesn't go out of bounds
            scrollbarY = Math.max(listAreaY, Math.min(scrollbarY, listAreaY + listAreaHeight - scrollbarHeight));

            // Draw scrollbar handle - rounded corners effect with more opacity
            drawRoundedRect(context, leftMargin + contentWidth - 8, scrollbarY, 4, scrollbarHeight, 0xC0FFFFFF);
        }

        // Draw candidate list with scrolling
        int candidateY = listAreaY - electionScrollOffset;
        int visibleCandidates = 0;

        for (TownPlayer candidate : candidates) {
            // Skip if candidate is completely outside visible area
            if (candidateY + rowHeight < listAreaY || candidateY > listAreaY + listAreaHeight) {
                candidateY += rowHeight;
                continue;
            }

            // Draw candidate entry background (alternating colors)
            int bgColor = visibleCandidates % 2 == 0 ? 0x20FFFFFF : 0x30FFFFFF;
            context.fill(leftMargin, candidateY, leftMargin + contentWidth - 30, candidateY + rowHeight, bgColor);

            // Draw rank icon and name with color
            TownPlayerRank rank = candidate.getRank();
            // Draw rank icon
            context.drawTextWithShadow(this.textRenderer, rank.getIcon(),
                leftMargin + 5, candidateY + 5, rank.getColor());
            // Draw rank name
            context.drawTextWithShadow(this.textRenderer, getRankText(rank),
                leftMargin + 15, candidateY + 5, rank.getColor());

            // Draw player name
            context.drawTextWithShadow(this.textRenderer, candidate.getName(),
                leftMargin + rankWidth + 5, candidateY + 5, 0xFFFFFF);

            // Draw vote count
            int votes = voteCount.getOrDefault(candidate.getUuid(), 0);
            context.drawTextWithShadow(this.textRenderer, String.valueOf(votes),
                leftMargin + rankWidth + nameWidth + 5, candidateY + 5, 0xFFFFFF);

            // Draw info button
            int infoX = leftMargin + rankWidth + nameWidth + votesWidth + 5;
            int infoY = candidateY + 1;
            int infoButtonWidth = 30;
            int infoButtonHeight = 16;
            boolean infoHovered = mouseX >= infoX && mouseX <= infoX + infoButtonWidth &&
                                 mouseY >= infoY && mouseY <= infoY + infoButtonHeight;

            drawModernButton(context, infoX, infoY, infoButtonWidth, infoButtonHeight, 0xFF2196F3, // Blue
                infoHovered, true);
            context.drawCenteredTextWithShadow(this.textRenderer, "Info",
                infoX + infoButtonWidth / 2, infoY + 4, 0xFFFFFF);

            // Draw vote button (or voted indicator)
            boolean isVotedFor = hasVoted && candidate.getUuid().equals(votedFor);

            if (isVotedFor) {
                // Show voted indicator
                int voteX = infoX + infoButtonWidth + 5;
                int voteY = candidateY + 1;
                int voteButtonWidth = 30;
                int voteButtonHeight = 16;

                drawModernButton(context, voteX, voteY, voteButtonWidth, voteButtonHeight, 0xFF55FF55, // Green
                    false, true);
                context.drawCenteredTextWithShadow(this.textRenderer, "✓",
                    voteX + voteButtonWidth / 2, voteY + 4, 0xFFFFFF);
            } else if (!hasVoted) {
                // Draw vote button
                int voteX = infoX + infoButtonWidth + 5;
                int voteY = candidateY + 1;
                int voteButtonWidth = 30;
                int voteButtonHeight = 16;
                boolean voteHovered = mouseX >= voteX && mouseX <= voteX + voteButtonWidth &&
                                     mouseY >= voteY && mouseY <= voteY + voteButtonHeight;

                drawModernButton(context, voteX, voteY, voteButtonWidth, voteButtonHeight, 0xFF4CAF50, // Green
                    voteHovered, true);
                context.drawCenteredTextWithShadow(this.textRenderer, "Vote",
                    voteX + voteButtonWidth / 2, voteY + 4, 0xFFFFFF);
            }

            candidateY += rowHeight;
            visibleCandidates++;
        }

        // If no candidates, show a message
        if (candidates.isEmpty()) {
            context.drawCenteredTextWithShadow(this.textRenderer,
                    Text.literal("No candidates available.").formatted(Formatting.ITALIC),
                    contentX + contentWidth / 2, listAreaY + 30, 0xFFFFFF);
        }
    }

    /**
     * Gets the name of a player from their UUID.
     *
     * @param playerId The player's UUID
     * @return The player's name, or "Unknown Player" if not found
     */
    private String getPlayerName(UUID playerId) {
        // In a real implementation, this would look up the player's name
        // For now, we'll just return a placeholder
        return "Player " + playerId.toString().substring(0, 8);
    }

    /**
     * Renders the Chat subcategory content with modern design - full area chat without header.
     */
    private void renderChatSubcategory(DrawContext context, int mouseX, int mouseY, Town playerTown, int contentX, int contentY, int contentWidth, int contentHeight) {
        // Use the full content area for chat - no header section
        int leftMargin = contentX + 8; // Slightly increased margin for better aesthetics
        int topMargin = contentY + 8;
        int rightMargin = 8;
        int bottomMargin = 8;

        // Calculate full chat area dimensions
        int chatAreaWidth = contentWidth - leftMargin + contentX - rightMargin;
        int chatAreaHeight = contentHeight - topMargin + contentY - bottomMargin;

        // Draw enhanced chat area background with improved glass effect
        drawEnhancedChatBackground(context, leftMargin, topMargin, chatAreaWidth, chatAreaHeight);

        // Initialize chat components to use the full area
        initializeChatComponents(leftMargin, topMargin, chatAreaWidth, chatAreaHeight, playerTown);

        // Render chat display area
        if (chatDisplayArea != null) {
            chatDisplayArea.render(context, mouseX, mouseY, playerTown.getId());

            // Auto-scroll to bottom when new messages arrive (with safety check)
            try {
                int currentMessageCount = TownChatClientManager.getInstance().getMessageCount(playerTown.getId());
                if (currentMessageCount > lastMessageCount && currentMessageCount > 0) {
                    chatDisplayArea.scrollToBottom();
                    lastMessageCount = currentMessageCount;
                }
            } catch (Exception e) {
                // Prevent crashes from auto-scroll
                Pokecobbleclaim.LOGGER.warn("Error during chat auto-scroll: " + e.getMessage());
            }
        }

        // Render chat input box
        if (chatInputBox != null) {
            chatInputBox.render(context, mouseX, mouseY);
        }

        // Render emoji picker on top of everything else
        if (chatInputBox != null) {
            chatInputBox.renderEmojiPicker(context, width, height, mouseX, mouseY);
        }
    }

    /**
     * Draws an enhanced chat background that matches the MyTownScreen aesthetic.
     */
    private void drawEnhancedChatBackground(DrawContext context, int x, int y, int width, int height) {
        // Main chat area background with deeper glass effect
        context.fill(x, y, x + width, y + height, GLASS_CONTENT_BG);

        // Enhanced glass effect borders for better visual depth
        context.fill(x, y, x + width, y + 2, GLASS_BRIGHT_HIGHLIGHT); // Thicker top highlight
        context.fill(x, y, x + 2, y + height, GLASS_BRIGHT_HIGHLIGHT); // Thicker left highlight

        // Subtle inner glow effect
        context.fill(x + 2, y + 2, x + width - 2, y + 3, GLASS_INNER_HIGHLIGHT);
        context.fill(x + 2, y + 2, x + 3, y + height - 2, GLASS_INNER_HIGHLIGHT);

        // Bottom and right subtle shadows for depth
        context.fill(x + width - 2, y + 2, x + width, y + height, GLASS_SHADOW);
        context.fill(x + 2, y + height - 2, x + width, y + height, GLASS_SHADOW);
    }

    /**
     * Renders the Settings subcategory content with modern design.
     */
    private void renderSettingsSubcategory(DrawContext context, int mouseX, int mouseY, Town playerTown, int contentX, int contentY, int contentWidth, int contentHeight) {
        // Modern layout with integrated header (matching other subcategories)
        int leftMargin = contentX + 5; // Reduced margin for more space
        int headerHeight = 28; // Reduced from 32 to 28 for more content space
        int currentY = contentY + 5; // Start closer to top

        // Draw modern header background with glass effect
        context.fill(leftMargin, currentY, leftMargin + contentWidth - 10, currentY + headerHeight, 0x60404040);
        context.fill(leftMargin, currentY, leftMargin + contentWidth - 10, currentY + 1, 0x20FFFFFF); // Top highlight
        context.fill(leftMargin, currentY, leftMargin + 1, currentY + headerHeight, 0x20FFFFFF); // Left highlight

        // Header layout - distribute controls across the header
        int headerPadding = 8;
        int controlY = currentY + (headerHeight - 16) / 2; // Center controls vertically in header
        int controlSpacing = 8;
        int currentX = leftMargin + headerPadding;

        // Header now only contains the Leave Town button

        // Leave Town button with smooth expandable animation
        String leaveIcon = "🚪"; // Door icon for leaving
        String leaveText = "Leave Town";
        int leaveIconColor = 0xE53935; // Red for destructive action

        // Calculate button dimensions with better proportions
        int leaveIconWidth = 24; // Larger icon-only width for better appearance
        int leaveFullWidth = 8 + this.textRenderer.getWidth(leaveIcon) + 6 + this.textRenderer.getWidth(leaveText) + 8; // Full expanded width with padding
        int leaveButtonHeight = 18; // Slightly taller for better proportions

        boolean currentLeaveHovered = mouseX >= currentX && mouseX <= currentX + leaveIconWidth &&
                                    mouseY >= controlY && mouseY <= controlY + leaveButtonHeight;

        // Update hover state and animation
        if (currentLeaveHovered != leaveButtonHovered) {
            leaveButtonHovered = currentLeaveHovered;
        }

        // Smooth animation progress (0.0 = collapsed, 1.0 = expanded)
        float leaveAnimationSpeed = 0.15f;
        if (leaveButtonHovered) {
            leaveButtonAnimation = Math.min(1.0f, leaveButtonAnimation + leaveAnimationSpeed);
        } else {
            leaveButtonAnimation = Math.max(0.0f, leaveButtonAnimation - leaveAnimationSpeed);
        }

        // Interpolate width smoothly
        int leaveCurrentWidth = (int)(leaveIconWidth + (leaveFullWidth - leaveIconWidth) * leaveButtonAnimation);

        // Draw leave button with enhanced styling
        int leaveCardColor = leaveButtonHovered ? 0x70505050 : 0x50404040; // Enhanced glass effect
        context.fill(currentX, controlY, currentX + leaveCurrentWidth, controlY + leaveButtonHeight, leaveCardColor);

        // Enhanced glass highlights with rounded corners effect
        context.fill(currentX + 1, controlY + 1, currentX + leaveCurrentWidth - 1, controlY + 2, 0x30FFFFFF);
        context.fill(currentX + 1, controlY + 1, currentX + 2, controlY + leaveButtonHeight - 1, 0x20FFFFFF);

        // Enhanced shadow
        context.fill(currentX + 1, controlY + leaveButtonHeight - 2, currentX + leaveCurrentWidth - 1, controlY + leaveButtonHeight - 1, 0x40000000);

        // Draw leave icon centered in collapsed state
        int leaveIconX = currentX + (leaveIconWidth - this.textRenderer.getWidth(leaveIcon)) / 2;
        int leaveTextY = controlY + (leaveButtonHeight - 8) / 2;

        // Always draw icon
        context.drawTextWithShadow(this.textRenderer, leaveIcon, leaveIconX, leaveTextY, leaveIconColor);

        // Draw text with smooth fade-in animation
        if (leaveButtonAnimation > 0.1f) {
            int leaveTextX = currentX + leaveIconWidth + 2;
            // Calculate text opacity based on animation progress
            float textAlpha = Math.max(0.0f, (leaveButtonAnimation - 0.3f) / 0.7f);
            int alpha = (int)(255 * textAlpha);
            int textColor = (alpha << 24) | 0x00FFFFFF;
            context.drawTextWithShadow(this.textRenderer, leaveText, leaveTextX, leaveTextY, textColor);
        }

        currentY += headerHeight + 10;

        // Content area - split into two zones horizontally (side by side)
        int contentAreaY = currentY;
        int availableHeight = Math.max(140, contentY + contentHeight - contentAreaY - 5); // Use full available height
        int zoneSpacing = 10; // Horizontal spacing between zones
        int availableWidth = contentWidth - 10; // Total available width for zones
        int zoneWidth = (availableWidth - zoneSpacing) / 2; // Split width into two zones with spacing
        int zoneHeight = availableHeight; // Use full available height for both zones

        // Ensure zones fit within content bounds
        if (zoneWidth < 150) { // Minimum width check
            zoneWidth = Math.max(150, availableWidth / 2); // Ensure minimum usable width
        }

        // Town Settings Zone (left side)
        renderTownSettingsZone(context, mouseX, mouseY, playerTown, leftMargin, contentAreaY, zoneWidth, zoneHeight);

        // Player Settings Zone (right side)
        int playerSettingsX = leftMargin + zoneWidth + zoneSpacing;
        renderPlayerSettingsZone(context, mouseX, mouseY, playerTown, playerSettingsX, contentAreaY, zoneWidth, zoneHeight);
    }

    /**
     * Renders the Town Settings zone within the Settings subcategory.
     */
    private void renderTownSettingsZone(DrawContext context, int mouseX, int mouseY, Town playerTown, int x, int y, int width, int height) {
        // Enhanced zone background with modern glass effect
        context.fill(x, y, x + width, y + height, 0x60303030); // Darker, more opaque background

        // Enhanced glass borders
        context.fill(x, y, x + width, y + 1, 0x40FFFFFF); // Brighter top highlight
        context.fill(x, y, x + 1, y + height, 0x30FFFFFF); // Left highlight
        context.fill(x + width - 1, y, x + width, y + height, 0x20FFFFFF); // Right highlight
        context.fill(x, y + height - 1, x + width, y + height, 0x40000000); // Bottom shadow

        // Inner glass effect for depth
        context.fill(x + 1, y + 1, x + width - 1, y + 2, 0x20FFFFFF);
        context.fill(x + 1, y + 1, x + 2, y + height - 1, 0x15FFFFFF);

        // Zone title with enhanced styling
        int titleY = y + 8;
        int titleX = x + 8;

        // Title background for better contrast
        int titleBgWidth = this.textRenderer.getWidth("Town Settings") + 8;
        context.fill(titleX - 2, titleY - 2, titleX + titleBgWidth, titleY + 10, 0x40000000);

        context.drawTextWithShadow(this.textRenderer, Text.literal("Town Settings").formatted(Formatting.BOLD),
            titleX, titleY, 0xFFFFFF);

        // Zone content area - scrollable
        int contentY = titleY + 18;
        int availableContentHeight = height - 28; // Leave space for title and padding
        int scrollableAreaX = x + 8;
        int scrollableAreaY = contentY;
        int scrollableAreaWidth = width - 16;
        int scrollableAreaHeight = availableContentHeight;

        // Enable scissor test for scrolling
        context.enableScissor(scrollableAreaX, scrollableAreaY, scrollableAreaX + scrollableAreaWidth, scrollableAreaY + scrollableAreaHeight);

        // Apply scroll offset
        int currentY = scrollableAreaY - townSettingsScrollOffset;

        // Button configuration - vertical layout with icons
        String[] buttonLabels = {"General Settings", "Town Personalisation", "Ban List"};
        String[] buttonIcons = {"⚙", "🏠", "🚫"};
        int[] buttonColors = {0xFF9C27B0, 0xFF4CAF50, 0xFFFF5722};

        int buttonWidth = scrollableAreaWidth - 10; // Full width minus padding
        int buttonHeight = 24; // Taller for better proportions
        int buttonSpacing = 6; // Spacing between buttons

        // Calculate total content height
        int totalContentHeight = buttonLabels.length * (buttonHeight + buttonSpacing) - buttonSpacing;

        // Render buttons vertically
        for (int i = 0; i < buttonLabels.length; i++) {
            int buttonX = scrollableAreaX + 5;
            int buttonY = currentY + i * (buttonHeight + buttonSpacing);

            // Check if button is visible in the scrollable area
            if (buttonY + buttonHeight >= scrollableAreaY && buttonY <= scrollableAreaY + scrollableAreaHeight) {
                boolean buttonHovered = mouseX >= buttonX && mouseX <= buttonX + buttonWidth &&
                                      mouseY >= buttonY && mouseY <= buttonY + buttonHeight;

                drawSettingsButtonWithIcon(context, buttonX, buttonY, buttonWidth, buttonHeight,
                                         buttonColors[i], buttonHovered, buttonIcons[i], buttonLabels[i]);
            }
        }

        // Disable scissor test
        context.disableScissor();

        // Draw scrollbar if needed
        int maxScrollOffset = Math.max(0, totalContentHeight - scrollableAreaHeight);
        if (maxScrollOffset > 0) {
            drawScrollbar(context, x + width - 6, scrollableAreaY, 4, scrollableAreaHeight,
                         townSettingsScrollOffset, totalContentHeight, scrollableAreaHeight);
        }
    }

    /**
     * Renders the Player Settings zone within the Settings subcategory.
     */
    private void renderPlayerSettingsZone(DrawContext context, int mouseX, int mouseY, Town playerTown, int x, int y, int width, int height) {
        // Enhanced zone background with modern glass effect
        context.fill(x, y, x + width, y + height, 0x60303030); // Darker, more opaque background

        // Enhanced glass borders
        context.fill(x, y, x + width, y + 1, 0x40FFFFFF); // Brighter top highlight
        context.fill(x, y, x + 1, y + height, 0x30FFFFFF); // Left highlight
        context.fill(x + width - 1, y, x + width, y + height, 0x20FFFFFF); // Right highlight
        context.fill(x, y + height - 1, x + width, y + height, 0x40000000); // Bottom shadow

        // Inner glass effect for depth
        context.fill(x + 1, y + 1, x + width - 1, y + 2, 0x20FFFFFF);
        context.fill(x + 1, y + 1, x + 2, y + height - 1, 0x15FFFFFF);

        // Zone title with enhanced styling
        int titleY = y + 8;
        int titleX = x + 8;

        // Title background for better contrast
        int titleBgWidth = this.textRenderer.getWidth("Player Settings") + 8;
        context.fill(titleX - 2, titleY - 2, titleX + titleBgWidth, titleY + 10, 0x40000000);

        context.drawTextWithShadow(this.textRenderer, Text.literal("Player Settings").formatted(Formatting.BOLD),
            titleX, titleY, 0xFFFFFF);

        // Zone content area - scrollable
        int contentY = titleY + 18;
        int availableContentHeight = height - 28; // Leave space for title and padding
        int scrollableAreaX = x + 8;
        int scrollableAreaY = contentY;
        int scrollableAreaWidth = width - 16;
        int scrollableAreaHeight = availableContentHeight;

        // Enable scissor test for scrolling
        context.enableScissor(scrollableAreaX, scrollableAreaY, scrollableAreaX + scrollableAreaWidth, scrollableAreaY + scrollableAreaHeight);

        // Apply scroll offset
        int currentY = scrollableAreaY - playerSettingsScrollOffset;

        // Button configuration - vertical layout with icons
        String[] buttonLabels = {"Notifications", "Performance", "Keybinds", "Audio Settings"};
        String[] buttonIcons = {"🔔", "⚡", "⌨", "🔊"};
        int[] buttonColors = {0xFFFF9800, 0xFF4CAF50, 0xFF9C27B0, 0xFFE91E63};

        int buttonWidth = scrollableAreaWidth - 10; // Full width minus padding
        int buttonHeight = 24; // Taller for better proportions
        int buttonSpacing = 6; // Spacing between buttons

        // Calculate total content height
        int totalContentHeight = buttonLabels.length * (buttonHeight + buttonSpacing) - buttonSpacing;

        // Render buttons vertically
        for (int i = 0; i < buttonLabels.length; i++) {
            int buttonX = scrollableAreaX + 5;
            int buttonY = currentY + i * (buttonHeight + buttonSpacing);

            // Check if button is visible in the scrollable area
            if (buttonY + buttonHeight >= scrollableAreaY && buttonY <= scrollableAreaY + scrollableAreaHeight) {
                boolean buttonHovered = mouseX >= buttonX && mouseX <= buttonX + buttonWidth &&
                                      mouseY >= buttonY && mouseY <= buttonY + buttonHeight;

                drawSettingsButtonWithIcon(context, buttonX, buttonY, buttonWidth, buttonHeight,
                                         buttonColors[i], buttonHovered, buttonIcons[i], buttonLabels[i]);
            }
        }

        // Disable scissor test
        context.disableScissor();

        // Draw scrollbar if needed
        int maxScrollOffset = Math.max(0, totalContentHeight - scrollableAreaHeight);
        if (maxScrollOffset > 0) {
            drawScrollbar(context, x + width - 6, scrollableAreaY, 4, scrollableAreaHeight,
                         playerSettingsScrollOffset, totalContentHeight, scrollableAreaHeight);
        }
    }

    /**
     * Handles click events for the Town Settings zone.
     */
    private boolean handleTownSettingsZoneClick(double mouseX, double mouseY, Town playerTown, int x, int y, int width, int height) {
        // Zone content area - match rendering exactly
        int titleY = y + 8;
        int contentY = titleY + 18;
        int scrollableAreaX = x + 8;
        int scrollableAreaY = contentY;
        int scrollableAreaWidth = width - 16;
        int scrollableAreaHeight = height - 28;

        // Check if click is within scrollable area
        if (mouseX < scrollableAreaX || mouseX > scrollableAreaX + scrollableAreaWidth ||
            mouseY < scrollableAreaY || mouseY > scrollableAreaY + scrollableAreaHeight) {
            return false;
        }

        // Apply scroll offset
        int currentY = scrollableAreaY - townSettingsScrollOffset;

        // Button configuration - match rendering exactly
        String[] buttonLabels = {"General Settings", "Town Personalisation", "Ban List"};

        int buttonWidth = scrollableAreaWidth - 10; // Full width minus padding
        int buttonHeight = 24; // Taller for better proportions
        int buttonSpacing = 6; // Spacing between buttons

        // Check button clicks
        for (int i = 0; i < buttonLabels.length; i++) {
            int buttonX = scrollableAreaX + 5;
            int buttonY = currentY + i * (buttonHeight + buttonSpacing);

            // Check if button is visible and clicked
            if (buttonY + buttonHeight >= scrollableAreaY && buttonY <= scrollableAreaY + scrollableAreaHeight &&
                mouseX >= buttonX && mouseX <= buttonX + buttonWidth &&
                mouseY >= buttonY && mouseY <= buttonY + buttonHeight) {

                // Play click sound
                SoundUtil.playButtonClickSound();

                // Handle different button actions
                if (i == 0) { // General Settings
                    // Check permission before opening settings screen
                    if (!PermissionChecker.checkPermissionOrShowNotification(this, playerTown, client.player.getUuid(),
                            PermissionChecker.Permissions.TOWN_SETTINGS,
                            PermissionChecker.Permissions.CAN_MODIFY_SETTINGS,
                            PermissionChecker.FeatureNames.TOWN_SETTINGS)) {
                        return true; // Permission denied notification was shown
                    }

                    // Permission granted, open settings screen in edit mode
                    this.client.setScreen(new CreateTownScreen(this, playerTown));
                } else if (i == 1) { // Town Personalisation
                    this.client.player.sendMessage(Text.literal("Town Personalisation - Coming Soon!"), false);
                } else if (i == 2) { // Ban List
                    this.client.player.sendMessage(Text.literal("Ban List - Coming Soon!"), false);
                }
                return true;
            }
        }

        return false;
    }

    /**
     * Handles click events for the Player Settings zone.
     */
    private boolean handlePlayerSettingsZoneClick(double mouseX, double mouseY, Town playerTown, int x, int y, int width, int height) {
        // Zone content area - match rendering exactly
        int titleY = y + 8;
        int contentY = titleY + 18;
        int scrollableAreaX = x + 8;
        int scrollableAreaY = contentY;
        int scrollableAreaWidth = width - 16;
        int scrollableAreaHeight = height - 28;

        // Check if click is within scrollable area
        if (mouseX < scrollableAreaX || mouseX > scrollableAreaX + scrollableAreaWidth ||
            mouseY < scrollableAreaY || mouseY > scrollableAreaY + scrollableAreaHeight) {
            return false;
        }

        // Apply scroll offset
        int currentY = scrollableAreaY - playerSettingsScrollOffset;

        // Button configuration - match rendering exactly
        String[] buttonLabels = {"Notifications", "Performance", "Keybinds", "Audio Settings"};
        String[] buttonMessages = {
            "Notification Settings - Coming Soon!",
            "Performance Settings - Coming Soon!",
            "Keybind Settings - Coming Soon!",
            "Audio Settings - Coming Soon!"
        };

        int buttonWidth = scrollableAreaWidth - 10; // Full width minus padding
        int buttonHeight = 24; // Taller for better proportions
        int buttonSpacing = 6; // Spacing between buttons

        // Check button clicks
        for (int i = 0; i < buttonLabels.length; i++) {
            int buttonX = scrollableAreaX + 5;
            int buttonY = currentY + i * (buttonHeight + buttonSpacing);

            // Check if button is visible and clicked
            if (buttonY + buttonHeight >= scrollableAreaY && buttonY <= scrollableAreaY + scrollableAreaHeight &&
                mouseX >= buttonX && mouseX <= buttonX + buttonWidth &&
                mouseY >= buttonY && mouseY <= buttonY + buttonHeight) {

                // Play click sound
                SoundUtil.playButtonClickSound();

                // Show placeholder message
                this.client.player.sendMessage(Text.literal(buttonMessages[i]), false);
                return true;
            }
        }

        return false;
    }

    /**
     * Renders the Jobs subcategory content with a button to open the dedicated JobsScreen.
     */
    private void renderJobsSubcategory(DrawContext context, int mouseX, int mouseY, Town playerTown, int contentX, int contentY, int contentWidth, int contentHeight) {
        // Add margins to ensure content stays within boundaries
        int margin = 20;
        int availableWidth = contentWidth - (margin * 2);
        int availableHeight = contentHeight - (margin * 2);

        // Calculate card size - use minimum of desired size and available space
        int desiredCardWidth = 400;
        int desiredCardHeight = 200;
        int cardWidth = Math.min(desiredCardWidth, availableWidth);
        int cardHeight = Math.min(desiredCardHeight, availableHeight);

        // Ensure minimum usable size
        cardWidth = Math.max(cardWidth, 250); // Minimum width for readability
        cardHeight = Math.max(cardHeight, 150); // Minimum height for content

        // Center the card within the available content area
        int centerX = contentX + contentWidth / 2;
        int centerY = contentY + contentHeight / 2;
        int cardX = centerX - cardWidth / 2;
        int cardY = centerY - cardHeight / 2;

        // Ensure card stays within content boundaries
        cardX = Math.max(contentX + margin, Math.min(cardX, contentX + contentWidth - margin - cardWidth));
        cardY = Math.max(contentY + margin, Math.min(cardY, contentY + contentHeight - margin - cardHeight));

        // Glass effect background
        context.fill(cardX, cardY, cardX + cardWidth, cardY + cardHeight, 0x60303030);
        context.fill(cardX, cardY, cardX + cardWidth, cardY + 1, 0x40FFFFFF); // Top highlight
        context.fill(cardX, cardY, cardX + 1, cardY + cardHeight, 0x20FFFFFF); // Left highlight
        context.fill(cardX + cardWidth - 1, cardY, cardX + cardWidth, cardY + cardHeight, 0x20000000); // Right shadow
        context.fill(cardX, cardY + cardHeight - 1, cardX + cardWidth, cardY + cardHeight, 0x40000000); // Bottom shadow

        // Title - positioned relative to card
        String title = "Town Jobs Management";
        int titleWidth = this.textRenderer.getWidth(title);
        int cardCenterX = cardX + cardWidth / 2;
        int titleX = cardCenterX - titleWidth / 2;
        int titleY = cardY + 30;
        context.drawTextWithShadow(this.textRenderer, Text.literal(title).formatted(Formatting.BOLD),
                titleX, titleY, 0xFFFFFF);

        // Description - positioned relative to card
        String description = "Manage jobs, applications, and view statistics";
        int descWidth = this.textRenderer.getWidth(description);
        int descX = cardCenterX - descWidth / 2;
        int descY = titleY + 20;
        context.drawTextWithShadow(this.textRenderer, description, descX, descY, 0xFFB0B0B0);

        // Job stats preview
        com.pokecobble.town.client.ClientTownJobsManager jobsManager = com.pokecobble.town.client.ClientTownJobsManager.getInstance();
        if (jobsManager.hasJobsData()) {
            java.util.List<com.pokecobble.town.TownJob.JobType> allJobs = java.util.Arrays.asList(com.pokecobble.town.TownJob.JobType.values());
            int unlockedCount = 0;
            int lockedCount = 0;

            for (com.pokecobble.town.TownJob.JobType jobType : allJobs) {
                if (jobType == com.pokecobble.town.TownJob.JobType.UNEMPLOYED) continue;
                com.pokecobble.town.TownJob job = jobsManager.getTownJob(jobType);
                if (job != null && job.isUnlocked()) {
                    unlockedCount++;
                } else {
                    lockedCount++;
                }
            }

            // Stats display - positioned relative to card
            String statsText = "Unlocked: " + unlockedCount + " | Locked: " + lockedCount;
            int statsWidth = this.textRenderer.getWidth(statsText);
            int statsX = cardCenterX - statsWidth / 2;
            int statsY = descY + 25;
            context.drawTextWithShadow(this.textRenderer, statsText, statsX, statsY, 0xFF4CAF50);
        }

        // Open Jobs Screen button - positioned relative to card and ensure it fits
        int buttonWidth = Math.min(200, cardWidth - 40); // Ensure button fits within card with margins
        int buttonHeight = 32;
        int buttonX = cardCenterX - buttonWidth / 2;
        int buttonY = cardY + cardHeight - 60;

        boolean buttonHovered = mouseX >= buttonX && mouseX <= buttonX + buttonWidth &&
                               mouseY >= buttonY && mouseY <= buttonY + buttonHeight;

        // Button background with glass effect
        int buttonBg = buttonHovered ? 0x80404040 : 0x60303030;
        context.fill(buttonX, buttonY, buttonX + buttonWidth, buttonY + buttonHeight, buttonBg);
        context.fill(buttonX, buttonY, buttonX + buttonWidth, buttonY + 1, 0x40FFFFFF); // Top highlight
        context.fill(buttonX, buttonY, buttonX + 1, buttonY + buttonHeight, 0x20FFFFFF); // Left highlight

        // Button accent color overlay
        int accentColor = buttonHovered ? 0x404CAF50 : 0x204CAF50;
        context.fill(buttonX, buttonY, buttonX + buttonWidth, buttonY + buttonHeight, accentColor);

        // Button text
        String buttonText = "🏢 Open Jobs Management";
        int buttonTextWidth = this.textRenderer.getWidth(buttonText);
        int buttonTextX = buttonX + (buttonWidth - buttonTextWidth) / 2;
        int buttonTextY = buttonY + (buttonHeight - 8) / 2;
        int textColor = buttonHovered ? 0xFFFFFFFF : 0xFFB0B0B0;
        context.drawTextWithShadow(this.textRenderer, buttonText, buttonTextX, buttonTextY, textColor);

        // Store button coordinates for click detection
        jobsButtonX = buttonX;
        jobsButtonY = buttonY;
        jobsButtonWidth = buttonWidth;
        jobsButtonHeight = buttonHeight;
    }

    /**
     * Calculates the height needed for expanded content based on enhanced information layout.
     */
    private int calculateExpandedContentHeight(com.pokecobble.town.TownJob.JobType jobType, com.pokecobble.town.TownJob job) {
        int lineHeight = 12;
        int sectionSpacing = 4;
        int padding = 6;
        int totalHeight = padding; // Start with top padding

        // === DESCRIPTION SECTION ===
        totalHeight += lineHeight + sectionSpacing;

        // === ROW 1: INCOME AND SLOTS ===
        totalHeight += lineHeight + sectionSpacing;

        // === ROW 2: REQUIREMENTS AND DIFFICULTY ===
        if (jobType != com.pokecobble.town.TownJob.JobType.UNEMPLOYED) {
            totalHeight += lineHeight + sectionSpacing;
        }

        // === ROW 3: FEES AND STATUS ===
        totalHeight += lineHeight + sectionSpacing;

        // === ACTION BUTTON ===
        if (jobType != com.pokecobble.town.TownJob.JobType.UNEMPLOYED &&
            job != null && job.isUnlocked()) {
            totalHeight += sectionSpacing + 18; // Button height
        }

        totalHeight += padding; // Bottom padding
        return totalHeight;
    }

    /**
     * Calculates the total content height for compact expandable job cards.
     */
    private int calculateJobsContentHeightCompact(java.util.List<com.pokecobble.town.TownJob.JobType> allJobs) {
        int totalHeight = 0;
        com.pokecobble.town.client.ClientTownJobsManager jobsManager = com.pokecobble.town.client.ClientTownJobsManager.getInstance();

        for (com.pokecobble.town.TownJob.JobType jobType : allJobs) {
            boolean isExpanded = expandedJobs.contains(jobType);
            float expansionProgress = jobExpansionAnimations.getOrDefault(jobType, 0.0f);

            int baseHeight = 28;
            com.pokecobble.town.TownJob job = jobsManager.getTownJob(jobType);
            int expandedContentHeight = calculateExpandedContentHeight(jobType, job);
            int currentCardHeight = (int) (baseHeight + (expandedContentHeight * expansionProgress));

            totalHeight += currentCardHeight;
        }

        return totalHeight;
    }

    /**
     * Gets filtered job list based on current filter type.
     */
    private java.util.List<com.pokecobble.town.TownJob.JobType> getFilteredJobList(com.pokecobble.town.client.ClientTownJobsManager jobsManager) {
        java.util.List<com.pokecobble.town.TownJob.JobType> allJobs = jobsManager.getAllAvailableJobs();
        java.util.List<com.pokecobble.town.TownJob.JobType> filteredJobs = new java.util.ArrayList<>();

        for (com.pokecobble.town.TownJob.JobType jobType : allJobs) {
            com.pokecobble.town.TownJob job = jobsManager.getTownJob(jobType);

            switch (currentJobFilterType) {
                case ALL:
                    filteredJobs.add(jobType);
                    break;
                case UNLOCKED:
                    if (job != null && job.isUnlocked()) {
                        filteredJobs.add(jobType);
                    }
                    break;
                case LOCKED:
                    if (job == null || !job.isUnlocked()) {
                        filteredJobs.add(jobType);
                    }
                    break;
            }
        }

        return filteredJobs;
    }

    /**
     * Renders a compact expandable job card matching the modern style.
     */
    private void renderCompactJobCard(DrawContext context, int mouseX, int mouseY, com.pokecobble.town.TownJob.JobType jobType,
                                     com.pokecobble.town.TownJob job, int cardX, int cardY, int cardWidth, int cardHeight,
                                     int listAreaY, int listAreaHeight) {

        // Check if this job is expanded
        boolean isExpanded = expandedJobs.contains(jobType);
        float expansionProgress = jobExpansionAnimations.getOrDefault(jobType, 0.0f);

        // Calculate actual card height with expansion
        int baseHeight = 28;
        int expandedContentHeight = calculateExpandedContentHeight(jobType, job);
        int actualCardHeight = (int) (baseHeight + (expandedContentHeight * expansionProgress));

        // Check if mouse is hovering over this job card
        boolean isHovered = mouseX >= cardX && mouseX <= cardX + cardWidth &&
                           mouseY >= cardY && mouseY <= cardY + actualCardHeight &&
                           mouseY >= listAreaY && mouseY <= listAreaY + listAreaHeight;

        // Draw modern job card background with glass effect
        int cardColor = isHovered ? 0x60404040 : 0x40303030;
        context.fill(cardX, cardY, cardX + cardWidth, cardY + actualCardHeight, cardColor);

        // Draw subtle glass highlight at the top
        context.fill(cardX, cardY, cardX + cardWidth, cardY + 1, 0x20FFFFFF);
        context.fill(cardX, cardY, cardX + 1, cardY + actualCardHeight, 0x20FFFFFF);

        // Draw subtle shadow at the bottom
        context.fill(cardX, cardY + actualCardHeight - 1, cardX + cardWidth, cardY + actualCardHeight, 0x20000000);

        // === BASE CARD CONTENT ===
        // Draw expand/collapse button on the left
        int expandButtonX = cardX + 8;
        int expandButtonY = cardY + (baseHeight - 8) / 2;
        boolean expandButtonHovered = mouseX >= cardX && mouseX <= cardX + cardWidth &&
                                    mouseY >= cardY && mouseY <= cardY + baseHeight; // Whole header is clickable

        int expandButtonColor = expandButtonHovered ? 0xFF2196F3 : 0xFF666666;
        String expandIcon = isExpanded ? "▼" : "▶";
        context.drawTextWithShadow(this.textRenderer, expandIcon, expandButtonX, expandButtonY, expandButtonColor);

        // Draw job icon and name with modern styling
        int iconX = cardX + 25; // After expand button
        int iconY = cardY + (baseHeight - 8) / 2; // Center vertically in base area

        // Draw job icon with enhanced visibility
        context.drawTextWithShadow(this.textRenderer, jobType.getIcon(),
            iconX, iconY, 0xFFFFFF);

        // Calculate icon width for positioning
        int iconWidth = this.textRenderer.getWidth(jobType.getIcon());

        // Draw job name with modern styling
        String jobName = jobType.getDisplayName();
        int nameX = iconX + iconWidth + 8; // Position after icon
        context.drawTextWithShadow(this.textRenderer, Text.literal(jobName).formatted(Formatting.BOLD),
            nameX, iconY, 0xFFFFFF);

        // Draw max slots info
        String slotsText;
        if (jobType.getMaxSlots() == -1) {
            slotsText = "Slots: ∞";
        } else {
            // TODO: Implement actual player assignment tracking
            int currentAssigned = 0; // Placeholder
            slotsText = "Slots: " + currentAssigned + "/" + jobType.getMaxSlots();
        }
        int slotsX = cardX + cardWidth - this.textRenderer.getWidth(slotsText) - 90; // Leave space for status
        context.drawTextWithShadow(this.textRenderer, slotsText, slotsX, iconY, 0xFFCCCCCC);

        // Draw status indicator on the right side
        String statusText = "";
        int statusColor = 0xFFFFFF;
        if (job != null && job.isUnlocked()) {
            statusText = "✓ Unlocked";
            statusColor = 0x4CAF50;
        } else if (job != null && job.getApprovalStatus() == com.pokecobble.town.TownJob.ApprovalStatus.PENDING) {
            statusText = "⏳ Pending";
            statusColor = 0xFFFF9800;
        } else if (job != null && job.getApprovalStatus() == com.pokecobble.town.TownJob.ApprovalStatus.LOCKED) {
            statusText = "🔒 Locked";
            statusColor = 0x9E9E9E;
        } else if (jobType != com.pokecobble.town.TownJob.JobType.UNEMPLOYED) {
            statusText = "🔒 Locked";
            statusColor = 0x9E9E9E;
        } else {
            statusText = "Default";
            statusColor = 0xFFFFFF;
        }

        int statusX = cardX + cardWidth - this.textRenderer.getWidth(statusText) - 10; // Right side of card
        context.drawTextWithShadow(this.textRenderer, statusText, statusX, iconY, statusColor);

        // === EXPANDED CONTENT ===
        if (expansionProgress > 0.0f) {
            int expandedY = cardY + baseHeight;
            int expandedRenderHeight = (int) (expandedContentHeight * expansionProgress);

            // Enable scissor for smooth expansion animation
            context.enableScissor(cardX, expandedY, cardX + cardWidth, expandedY + expandedRenderHeight);

            renderExpandedJobContent(context, jobType, job, cardX, expandedY, cardWidth, expansionProgress);

            context.disableScissor();
        }
    }

    /**
     * Renders the expanded content for a job card with enhanced information display.
     */
    private void renderExpandedJobContent(DrawContext context, com.pokecobble.town.TownJob.JobType jobType,
                                         com.pokecobble.town.TownJob job, int cardX, int expandedY, int cardWidth, float expansionProgress) {
        int padding = 15;
        int leftX = cardX + padding;
        int currentY = expandedY + 6;
        int lineHeight = 12;
        int sectionSpacing = 4;
        int contentWidth = cardWidth - (padding * 2);

        // === DESCRIPTION SECTION ===
        context.drawTextWithShadow(this.textRenderer, "📋", leftX, currentY, 0xFFCCCCCC);
        String description = jobType.getDescription();
        context.drawTextWithShadow(this.textRenderer, " " + description, leftX + 12, currentY, 0xFFE0E0E0);
        currentY += lineHeight + sectionSpacing;

        // === ROW 1: INCOME AND SLOTS ===
        int row1Y = currentY;

        // Left side - Income section
        context.drawTextWithShadow(this.textRenderer, "💰", leftX, row1Y, 0xFFD700);
        String payText = " " + jobType.getDailyPay() + " coins/day";
        context.drawTextWithShadow(this.textRenderer, payText, leftX + 12, row1Y, 0xFFD700);

        // Right side - Available slots
        int rightSideX = leftX + contentWidth / 2;
        context.drawTextWithShadow(this.textRenderer, "👥", rightSideX, row1Y, 0xFF4CAF50);
        String slotsText;
        if (jobType.getMaxSlots() == -1) {
            slotsText = " Unlimited slots";
        } else {
            // TODO: Implement actual player assignment tracking
            int currentAssigned = 0; // Placeholder
            slotsText = " " + currentAssigned + "/" + jobType.getMaxSlots() + " slots";
        }
        context.drawTextWithShadow(this.textRenderer, slotsText, rightSideX + 12, row1Y, 0xFF4CAF50);
        currentY += lineHeight + sectionSpacing;

        // === ROW 2: REQUIREMENTS AND DIFFICULTY ===
        if (jobType != com.pokecobble.town.TownJob.JobType.UNEMPLOYED) {
            int row2Y = currentY;

            // Left side - Building requirement
            if (jobType.getRequiredBuilding() != null) {
                context.drawTextWithShadow(this.textRenderer, "🏗", leftX, row2Y, 0xFFFFAA);
                String reqText = " " + jobType.getRequiredBuilding();
                context.drawTextWithShadow(this.textRenderer, reqText, leftX + 12, row2Y, 0xFFFFAA);
            }

            // Right side - Difficulty
            int difficultyTier = jobType.getDifficultyTier();
            context.drawTextWithShadow(this.textRenderer, "⭐", rightSideX, row2Y, 0xFFFF9800);
            String difficultyText = " Difficulty: ";
            context.drawTextWithShadow(this.textRenderer, difficultyText, rightSideX + 12, row2Y, 0xFFCCCCCC);
            int starsX = rightSideX + 12 + this.textRenderer.getWidth(difficultyText);
            drawDifficultyStars(context, starsX, row2Y, difficultyTier);
            currentY += lineHeight + sectionSpacing;
        }

        // === ROW 3: FEES AND STATUS ===
        int row3Y = currentY;

        // Left side - Fees (if applicable)
        if (jobType != com.pokecobble.town.TownJob.JobType.UNEMPLOYED) {
            if (jobType.getUnlockFee() > 0) {
                context.drawTextWithShadow(this.textRenderer, "🔓", leftX, row3Y, 0xFFFFAA);
                String feeText = " Unlock: " + formatCoins(jobType.getUnlockFee()) + " (town pays)";
                context.drawTextWithShadow(this.textRenderer, feeText, leftX + 12, row3Y, 0xFFFFAA);
            } else if (jobType.getApplyFee() > 0) {
                context.drawTextWithShadow(this.textRenderer, "💳", leftX, row3Y, 0xFFFF9800);
                String applyFeeText = " Apply: " + formatCoins(jobType.getApplyFee()) + " (you pay)";
                context.drawTextWithShadow(this.textRenderer, applyFeeText, leftX + 12, row3Y, 0xFFFF9800);
            }
        }

        // Right side - Status
        if (job == null) {
            context.drawTextWithShadow(this.textRenderer, "🔒", rightSideX, row3Y, 0xFF9E9E9E);
            context.drawTextWithShadow(this.textRenderer, " Not Available", rightSideX + 12, row3Y, 0xFF9E9E9E);
        } else {
            String statusIcon = job.getApprovalStatus().getIcon();
            String statusText = " " + job.getApprovalStatus().getDisplayName();
            int statusColor = getStatusColor(job.getApprovalStatus());

            context.drawTextWithShadow(this.textRenderer, statusIcon, rightSideX, row3Y, statusColor);
            context.drawTextWithShadow(this.textRenderer, statusText, rightSideX + 12, row3Y, statusColor);
        }
        currentY += lineHeight + sectionSpacing;

        // === ACTION BUTTON ===
        // Enhanced action button for unlocked jobs
        if (jobType != com.pokecobble.town.TownJob.JobType.UNEMPLOYED &&
            job != null && job.isUnlocked()) {

            currentY += sectionSpacing; // Add space before button

            // Draw enhanced action button with glass effect
            int buttonWidth = 130;
            int buttonHeight = 18;
            int buttonX = cardX + cardWidth - buttonWidth - padding;
            int buttonY = currentY;

            // Button background with glass effect
            context.fill(buttonX, buttonY, buttonX + buttonWidth, buttonY + buttonHeight, 0x80404040);
            context.fill(buttonX, buttonY, buttonX + buttonWidth, buttonY + 1, 0x60FFFFFF); // Top highlight
            context.fill(buttonX, buttonY, buttonX + 1, buttonY + buttonHeight, 0x60FFFFFF); // Left highlight
            context.fill(buttonX, buttonY + buttonHeight - 1, buttonX + buttonWidth, buttonY + buttonHeight, 0x40000000); // Bottom shadow

            // Button icon and text
            String buttonIcon = "💼";
            String buttonText = " Apply Now";
            int iconX = buttonX + 8;
            int textX = iconX + this.textRenderer.getWidth(buttonIcon) + 2;
            int textY = buttonY + (buttonHeight - 8) / 2;

            context.drawTextWithShadow(this.textRenderer, buttonIcon, iconX, textY, 0xFF4CAF50);
            context.drawTextWithShadow(this.textRenderer, buttonText, textX, textY, 0xFF4CAF50);
        }
    }

    /**
     * Wraps text to fit within a specified width.
     */
    private java.util.List<String> wrapText(String text, int maxWidth) {
        java.util.List<String> lines = new java.util.ArrayList<>();
        String[] words = text.split(" ");
        StringBuilder currentLine = new StringBuilder();

        for (String word : words) {
            String testLine = currentLine.length() == 0 ? word : currentLine + " " + word;
            if (this.textRenderer.getWidth(testLine) <= maxWidth) {
                currentLine = new StringBuilder(testLine);
            } else {
                if (currentLine.length() > 0) {
                    lines.add(currentLine.toString());
                    currentLine = new StringBuilder(word);
                } else {
                    lines.add(word); // Single word is too long, add it anyway
                }
            }
        }

        if (currentLine.length() > 0) {
            lines.add(currentLine.toString());
        }

        return lines;
    }

    /**
     * Draws a section header with icon and title for job information.
     */
    private void drawJobInfoSection(DrawContext context, int x, int y, int width, String icon, String title, int color) {
        // Draw section background
        context.fill(x - 5, y - 2, x + width + 5, y + 12, 0x20000000);

        // Draw icon
        context.drawTextWithShadow(this.textRenderer, icon, x, y, color);

        // Draw title
        int titleX = x + this.textRenderer.getWidth(icon) + 4;
        context.drawTextWithShadow(this.textRenderer, title, titleX, y, color);

        // Draw subtle underline
        int lineY = y + 10;
        context.fill(x, lineY, x + width, lineY + 1, 0x40FFFFFF);
    }

    /**
     * Draws an income level indicator bar.
     */
    private void drawIncomeBar(DrawContext context, int x, int y, int width, int height, long dailyPay) {
        // Background bar
        context.fill(x, y, x + width, y + height, 0x40000000);

        // Calculate income level (0-1000 coins = low, 1000-5000 = medium, 5000+ = high)
        float incomeLevel;
        int barColor;

        if (dailyPay <= 1000) {
            incomeLevel = dailyPay / 1000.0f;
            barColor = 0xFFFF9800; // Orange for low income
        } else if (dailyPay <= 5000) {
            incomeLevel = 0.3f + ((dailyPay - 1000) / 4000.0f) * 0.4f; // 30-70%
            barColor = 0xFFFFD700; // Gold for medium income
        } else {
            incomeLevel = 0.7f + Math.min((dailyPay - 5000) / 10000.0f, 0.3f); // 70-100%
            barColor = 0xFF4CAF50; // Green for high income
        }

        int fillWidth = (int)(width * Math.min(incomeLevel, 1.0f));
        context.fill(x, y, x + fillWidth, y + height, barColor);

        // Add highlight
        context.fill(x, y, x + fillWidth, y + 1, 0x60FFFFFF);
    }

    /**
     * Draws difficulty stars for job difficulty indication.
     */
    private void drawDifficultyStars(DrawContext context, int x, int y, int difficulty) {
        String[] stars = {"⭐", "⭐⭐", "⭐⭐⭐"};
        int[] colors = {0xFF4CAF50, 0xFFFF9800, 0xFFFF5722};

        if (difficulty >= 1 && difficulty <= 3) {
            String starText = stars[difficulty - 1];
            int color = colors[difficulty - 1];
            context.drawTextWithShadow(this.textRenderer, starText, x, y, color);
        }
    }

    /**
     * Draws a slots availability progress bar.
     */
    private void drawSlotsBar(DrawContext context, int x, int y, int width, int height, int current, int max) {
        // Background bar
        context.fill(x, y, x + width, y + height, 0x40000000);

        // Calculate fill percentage
        float fillPercentage = max > 0 ? (float)current / max : 0.0f;
        int fillWidth = (int)(width * fillPercentage);

        // Choose color based on availability
        int barColor;
        if (fillPercentage < 0.5f) {
            barColor = 0xFF4CAF50; // Green - plenty of slots
        } else if (fillPercentage < 0.8f) {
            barColor = 0xFFFF9800; // Orange - getting full
        } else {
            barColor = 0xFFFF5722; // Red - almost full
        }

        context.fill(x, y, x + fillWidth, y + height, barColor);

        // Add highlight
        context.fill(x, y, x + fillWidth, y + 1, 0x60FFFFFF);
    }

    /**
     * Gets the appropriate color for a job approval status.
     */
    private int getStatusColor(com.pokecobble.town.TownJob.ApprovalStatus status) {
        switch (status) {
            case PENDING:
                return 0xFFFF9800; // Orange
            case APPROVED:
            case UNLOCKED:
                return 0xFF4CAF50; // Green
            case REJECTED:
                return 0xFFFF5722; // Red
            case LOCKED:
            default:
                return 0xFF9E9E9E; // Gray
        }
    }

    /**
     * Formats coin amounts with appropriate suffixes (K, M, etc.).
     */
    private String formatCoins(long amount) {
        if (amount >= 1000000) {
            return String.format("%.1fM", amount / 1000000.0);
        } else if (amount >= 1000) {
            return String.format("%.1fK", amount / 1000.0);
        } else {
            return String.valueOf(amount);
        }
    }

    /**
     * Calculates the total content height needed for all job cards.
     */
    private int calculateJobsContentHeight(java.util.List<com.pokecobble.town.TownJob.JobType> allJobs, com.pokecobble.town.client.ClientTownJobsManager jobsManager) {
        int totalHeight = 0;
        int jobSpacing = 6;

        for (com.pokecobble.town.TownJob.JobType jobType : allJobs) {
            boolean isExpanded = expandedJobs.contains(jobType);
            float expansionProgress = jobExpansionAnimations.getOrDefault(jobType, 0.0f);

            int baseHeight = 50;
            int expandedHeight = 120; // Match the rendering height
            int currentJobHeight = (int) (baseHeight + (expandedHeight * expansionProgress));

            totalHeight += currentJobHeight + jobSpacing;
        }

        return totalHeight;
    }

    /**
     * Updates job expansion animations.
     */
    private void updateJobExpansionAnimations() {
        float animationSpeed = 0.12f;

        // Update expansion animations for all job types
        for (com.pokecobble.town.TownJob.JobType jobType : com.pokecobble.town.TownJob.JobType.values()) {
            boolean shouldBeExpanded = expandedJobs.contains(jobType);
            float currentAnimation = jobExpansionAnimations.getOrDefault(jobType, 0.0f);

            if (shouldBeExpanded) {
                currentAnimation = Math.min(1.0f, currentAnimation + animationSpeed);
            } else {
                currentAnimation = Math.max(0.0f, currentAnimation - animationSpeed);
            }

            if (currentAnimation > 0.0f) {
                jobExpansionAnimations.put(jobType, currentAnimation);
            } else {
                jobExpansionAnimations.remove(jobType);
            }
        }
    }

    /**
     * Renders an individual job card with expandable content.
     */
    private void renderJobCard(DrawContext context, int mouseX, int mouseY, com.pokecobble.town.TownJob.JobType jobType,
                              com.pokecobble.town.TownJob job, int cardX, int cardY, int cardWidth, int cardHeight,
                              boolean isExpanded, float expansionProgress) {

        int padding = 10;
        int iconX = cardX + padding;
        int iconY = cardY + 8;

        // Job icon (larger and more prominent)
        context.drawTextWithShadow(this.textRenderer, jobType.getIcon(), iconX, iconY, 0xFFFFFF);

        // Job name and basic info
        int nameX = iconX + 25;
        context.drawTextWithShadow(this.textRenderer, jobType.getDisplayName(), nameX, iconY, 0xFFFFFF);

        // Status indicator next to name
        String statusText = "";
        int statusColor = 0xFFFFFF;
        if (job != null && job.isUnlocked()) {
            statusText = " ✓";
            statusColor = 0xFF4CAF50;
        } else if (job != null && job.getApprovalStatus() == com.pokecobble.town.TownJob.ApprovalStatus.PENDING) {
            statusText = " ⏳";
            statusColor = 0xFFFF9800;
        } else if (job != null && job.getApprovalStatus() == com.pokecobble.town.TownJob.ApprovalStatus.LOCKED) {
            statusText = " 🔒";
            statusColor = 0xFF9E9E9E;
        } else if (jobType != com.pokecobble.town.TownJob.JobType.UNEMPLOYED) {
            statusText = " 🔒";
            statusColor = 0xFF9E9E9E;
        }

        if (!statusText.isEmpty()) {
            int statusX = nameX + this.textRenderer.getWidth(jobType.getDisplayName()) + 5;
            context.drawTextWithShadow(this.textRenderer, statusText, statusX, iconY, statusColor);
        }

        // Daily pay (always visible)
        int payY = iconY + 12;
        String payText = "💰 " + jobType.getDailyPay() + " coins/day";
        context.drawTextWithShadow(this.textRenderer, payText, nameX, payY, 0xFFD700);

        // Expand/Collapse button
        int expandButtonX = cardX + cardWidth - 30;
        int expandButtonY = cardY + 8;
        int expandButtonSize = 16;
        boolean expandButtonHovered = mouseX >= expandButtonX && mouseX <= expandButtonX + expandButtonSize &&
                                    mouseY >= expandButtonY && mouseY <= expandButtonY + expandButtonSize;

        int expandButtonColor = expandButtonHovered ? 0xFF2196F3 : 0xFF666666;
        String expandIcon = isExpanded ? "▼" : "▶";
        context.drawTextWithShadow(this.textRenderer, expandIcon, expandButtonX, expandButtonY, expandButtonColor);

        // Action button (unlock/status)
        int buttonX = cardX + cardWidth - 120;
        int buttonY = cardY + 8;
        int buttonWidth = 80;
        int buttonHeight = 16;

        if (job != null && job.isUnlocked()) {
            // Show unlocked status
            context.fill(buttonX, buttonY, buttonX + buttonWidth, buttonY + buttonHeight, 0xFF4CAF50);
            context.drawCenteredTextWithShadow(this.textRenderer, "Unlocked",
                buttonX + buttonWidth / 2, buttonY + 4, 0xFFFFFF);
        } else if (job != null && job.getApprovalStatus() == com.pokecobble.town.TownJob.ApprovalStatus.PENDING) {
            // Show pending status
            context.fill(buttonX, buttonY, buttonX + buttonWidth, buttonY + buttonHeight, 0xFFFF9800);
            context.drawCenteredTextWithShadow(this.textRenderer, "Pending",
                buttonX + buttonWidth / 2, buttonY + 4, 0xFFFFFF);
        } else if (job != null && job.getApprovalStatus() == com.pokecobble.town.TownJob.ApprovalStatus.LOCKED) {
            // Show locked status
            context.fill(buttonX, buttonY, buttonX + buttonWidth, buttonY + buttonHeight, 0xFF9E9E9E);
            context.drawCenteredTextWithShadow(this.textRenderer, "Locked",
                buttonX + buttonWidth / 2, buttonY + 4, 0xFFFFFF);
        } else if (jobType != com.pokecobble.town.TownJob.JobType.UNEMPLOYED) {
            // Show unlock button
            boolean buttonHovered = mouseX >= buttonX && mouseX <= buttonX + buttonWidth &&
                                  mouseY >= buttonY && mouseY <= buttonY + buttonHeight;
            int buttonColor = buttonHovered ? 0xFF1976D2 : 0xFF2196F3;
            context.fill(buttonX, buttonY, buttonX + buttonWidth, buttonY + buttonHeight, buttonColor);

            String unlockText = "Unlock";
            context.drawCenteredTextWithShadow(this.textRenderer, unlockText,
                buttonX + buttonWidth / 2, buttonY + 4, 0xFFFFFF);
        }

        // Expanded content (animated)
        if (expansionProgress > 0.0f) {
            int expandedY = cardY + 35;
            int expandedContentHeight = (int) (85 * expansionProgress); // Max expanded height (increased for better structure)

            // Enable scissor for smooth expansion animation
            context.enableScissor(cardX, expandedY, cardX + cardWidth, expandedY + expandedContentHeight);

            renderExpandedJobContent(context, jobType, job, cardX, expandedY, cardWidth, expansionProgress);

            context.disableScissor();
        }
    }

    /**
     * Draws a scrollbar for the jobs list.
     */
    private void drawJobsScrollbar(DrawContext context, int scrollbarX, int scrollbarY, int scrollbarHeight, int scrollOffset, int maxScrollOffset) {
        int scrollbarWidth = 8;

        // Scrollbar track
        context.fill(scrollbarX, scrollbarY, scrollbarX + scrollbarWidth, scrollbarY + scrollbarHeight, 0x40000000);

        // Scrollbar thumb
        if (maxScrollOffset > 0) {
            float scrollRatio = (float) scrollOffset / maxScrollOffset;
            float thumbRatio = (float) scrollbarHeight / (scrollbarHeight + maxScrollOffset);

            int thumbHeight = Math.max(20, (int) (scrollbarHeight * thumbRatio));
            int thumbY = scrollbarY + (int) (scrollRatio * (scrollbarHeight - thumbHeight));

            context.fill(scrollbarX + 1, thumbY, scrollbarX + scrollbarWidth - 1, thumbY + thumbHeight, 0x80FFFFFF);
        }
    }

    private String getAccessIcon(Town.JoinType joinType) {
        switch (joinType) {
            case OPEN: return "✓";
            case CLOSED: return "✗";
            case INVITE_ONLY: return "◐";
            default: return "?";
        }
    }

    private int getAccessColor(Town.JoinType joinType) {
        switch (joinType) {
            case OPEN: return 0x55FF55;
            case CLOSED: return 0xFF5555;
            case INVITE_ONLY: return 0x5555FF;
            default: return 0xAAAAAA;
        }
    }

    /**
     * Draws a settings button with glass effect design matching the new style.
     */
    private void drawSettingsButton(DrawContext context, int x, int y, int width, int height, int color, boolean hovered, String text) {
        // Extract RGB components from the accent color
        int red = (color >> 16) & 0xFF;
        int green = (color >> 8) & 0xFF;
        int blue = color & 0xFF;

        // Create glass effect background similar to zone background but with accent color tint
        // Base glass background with very low opacity
        int baseGlassAlpha = hovered ? 0x40 : 0x25; // Lower opacity for glass effect
        int baseGlassColor = (baseGlassAlpha << 24) | 0x303030; // Dark glass base similar to zone background

        // Accent color overlay with very low opacity to add subtle color tint
        int accentAlpha = hovered ? 0x30 : 0x18; // Very subtle accent tint
        int accentOverlay = (accentAlpha << 24) | (red << 16) | (green << 8) | blue;

        // Draw base glass background
        context.fill(x, y, x + width, y + height, baseGlassColor);

        // Draw subtle accent color overlay
        context.fill(x, y, x + width, y + height, accentOverlay);

        // Enhanced glass effect borders for better definition
        int topBorderColor = hovered ? 0x60FFFFFF : 0x40FFFFFF; // Brighter top highlight
        int sideBorderColor = hovered ? 0x40FFFFFF : 0x25FFFFFF; // Side highlights
        int bottomBorderColor = hovered ? 0x30000000 : 0x50000000; // Stronger bottom shadow
        int rightBorderColor = hovered ? 0x25000000 : 0x40000000; // Right shadow

        // Draw glass borders
        context.fill(x, y, x + width, y + 1, topBorderColor); // Top highlight
        context.fill(x, y, x + 1, y + height, sideBorderColor); // Left highlight
        context.fill(x + width - 1, y, x + width, y + height, rightBorderColor); // Right shadow
        context.fill(x, y + height - 1, x + width, y + height, bottomBorderColor); // Bottom shadow

        // Inner glass highlights for depth and frosted glass effect
        if (hovered) {
            // Stronger inner glow when hovered - creates elevated glass effect
            context.fill(x + 1, y + 1, x + width - 1, y + 2, 0x50FFFFFF); // Inner top highlight
            context.fill(x + 1, y + 1, x + 2, y + height - 1, 0x35FFFFFF); // Inner left highlight

            // Subtle inner accent glow for color definition
            int innerAccentAlpha = 0x20;
            int innerAccentGlow = (innerAccentAlpha << 24) | (red << 16) | (green << 8) | blue;
            context.fill(x + 2, y + 2, x + width - 2, y + 3, innerAccentGlow);

            // Add subtle inner shadow for pressed glass effect
            context.fill(x + 1, y + height - 2, x + width - 1, y + height - 1, 0x40000000);
            context.fill(x + width - 2, y + 1, x + width - 1, y + height - 1, 0x25000000);
        } else {
            // Subtle inner highlights for depth - frosted glass appearance
            context.fill(x + 1, y + 1, x + width - 1, y + 2, 0x30FFFFFF); // Inner top highlight
            context.fill(x + 1, y + 1, x + 2, y + height - 1, 0x20FFFFFF); // Inner left highlight

            // Very subtle inner accent tint
            int innerAccentAlpha = 0x12;
            int innerAccentTint = (innerAccentAlpha << 24) | (red << 16) | (green << 8) | blue;
            context.fill(x + 2, y + 2, x + width - 2, y + 3, innerAccentTint);
        }

        // Enhanced text with better contrast and subtle accent tinting
        int textColor = hovered ? 0xFFFFFFFF : 0xFFE0E0E0;
        int textWidth = this.textRenderer.getWidth(text);
        int textX = x + (width - textWidth) / 2;
        int textY = y + (height - this.textRenderer.fontHeight) / 2;

        // Draw text with shadow for better readability
        context.drawTextWithShadow(this.textRenderer, text, textX, textY, textColor);
    }

    /**
     * Draws a settings button with icon and text using glass effect design.
     */
    private void drawSettingsButtonWithIcon(DrawContext context, int x, int y, int width, int height, int color, boolean hovered, String icon, String text) {
        // Extract RGB components from the accent color
        int red = (color >> 16) & 0xFF;
        int green = (color >> 8) & 0xFF;
        int blue = color & 0xFF;

        // Create glass effect background similar to zone background but with accent color tint
        // Base glass background with very low opacity
        int baseGlassAlpha = hovered ? 0x40 : 0x25; // Lower opacity for glass effect
        int baseGlassColor = (baseGlassAlpha << 24) | 0x303030; // Dark glass base similar to zone background

        // Accent color overlay with very low opacity to add subtle color tint
        int accentAlpha = hovered ? 0x30 : 0x18; // Very subtle accent tint
        int accentOverlay = (accentAlpha << 24) | (red << 16) | (green << 8) | blue;

        // Draw base glass background
        context.fill(x, y, x + width, y + height, baseGlassColor);

        // Draw subtle accent color overlay
        context.fill(x, y, x + width, y + height, accentOverlay);

        // Enhanced glass effect borders for better definition
        int topBorderColor = hovered ? 0x60FFFFFF : 0x40FFFFFF; // Brighter top highlight
        int sideBorderColor = hovered ? 0x40FFFFFF : 0x25FFFFFF; // Side highlights
        int bottomBorderColor = hovered ? 0x30000000 : 0x50000000; // Stronger bottom shadow
        int rightBorderColor = hovered ? 0x25000000 : 0x40000000; // Right shadow

        // Draw glass borders
        context.fill(x, y, x + width, y + 1, topBorderColor); // Top highlight
        context.fill(x, y, x + 1, y + height, sideBorderColor); // Left highlight
        context.fill(x + width - 1, y, x + width, y + height, rightBorderColor); // Right shadow
        context.fill(x, y + height - 1, x + width, y + height, bottomBorderColor); // Bottom shadow

        // Inner glass highlights for depth and frosted glass effect
        if (hovered) {
            // Stronger inner glow when hovered - creates elevated glass effect
            context.fill(x + 1, y + 1, x + width - 1, y + 2, 0x50FFFFFF); // Inner top highlight
            context.fill(x + 1, y + 1, x + 2, y + height - 1, 0x35FFFFFF); // Inner left highlight

            // Subtle inner accent glow for color definition
            int innerAccentAlpha = 0x20;
            int innerAccentGlow = (innerAccentAlpha << 24) | (red << 16) | (green << 8) | blue;
            context.fill(x + 2, y + 2, x + width - 2, y + 3, innerAccentGlow);

            // Add subtle inner shadow for pressed glass effect
            context.fill(x + 1, y + height - 2, x + width - 1, y + height - 1, 0x40000000);
            context.fill(x + width - 2, y + 1, x + width - 1, y + height - 1, 0x25000000);
        } else {
            // Subtle inner highlights for depth - frosted glass appearance
            context.fill(x + 1, y + 1, x + width - 1, y + 2, 0x30FFFFFF); // Inner top highlight
            context.fill(x + 1, y + 1, x + 2, y + height - 1, 0x20FFFFFF); // Inner left highlight

            // Very subtle inner accent tint
            int innerAccentAlpha = 0x12;
            int innerAccentTint = (innerAccentAlpha << 24) | (red << 16) | (green << 8) | blue;
            context.fill(x + 2, y + 2, x + width - 2, y + 3, innerAccentTint);
        }

        // Draw icon and text
        int iconWidth = this.textRenderer.getWidth(icon);
        int textWidth = this.textRenderer.getWidth(text);
        int totalWidth = iconWidth + 6 + textWidth; // 6px spacing between icon and text

        int startX = x + (width - totalWidth) / 2;
        int textY = y + (height - this.textRenderer.fontHeight) / 2;

        // Enhanced colors with better contrast and subtle accent tinting
        if (hovered) {
            // When hovered, use bright white with subtle accent tint
            int iconColor = 0xFFFFFFFF;
            int textColor = 0xFFFFFFFF;

            // Add subtle accent glow to icon when hovered
            int accentGlowAlpha = 0x60;
            int iconAccentColor = (accentGlowAlpha << 24) | (red << 16) | (green << 8) | blue;

            // Draw icon with accent glow effect
            context.drawTextWithShadow(this.textRenderer, icon, startX, textY, iconAccentColor);
            context.drawTextWithShadow(this.textRenderer, icon, startX, textY, iconColor);

            // Draw text
            context.drawTextWithShadow(this.textRenderer, text, startX + iconWidth + 6, textY, textColor);
        } else {
            // Normal state with subtle accent tinting
            int baseTextAlpha = 0xE0;
            int iconColor = (baseTextAlpha << 24) | 0xE0E0E0;
            int textColor = (baseTextAlpha << 24) | 0xE0E0E0;

            // Draw icon and text
            context.drawTextWithShadow(this.textRenderer, icon, startX, textY, iconColor);
            context.drawTextWithShadow(this.textRenderer, text, startX + iconWidth + 6, textY, textColor);
        }
    }

    private void drawCompactButton(DrawContext context, int x, int y, int width, int height, int color, boolean hovered, String text) {
        // Draw button background
        int bgColor = hovered ? (color | 0x20FFFFFF) : color; // Lighter when hovered
        context.fill(x, y, x + width, y + height, bgColor);

        // Draw button border
        int borderColor = hovered ? 0xFFFFFFFF : 0xAAFFFFFF;
        context.fill(x, y, x + width, y + 1, borderColor); // Top
        context.fill(x, y + height - 1, x + width, y + height, borderColor); // Bottom
        context.fill(x, y, x + 1, y + height, borderColor); // Left
        context.fill(x + width - 1, y, x + width, y + height, borderColor); // Right

        // Draw text (centered)
        int textWidth = this.textRenderer.getWidth(text);
        int textX = x + (width - textWidth) / 2;
        int textY = y + (height - this.textRenderer.fontHeight) / 2;
        context.drawTextWithShadow(this.textRenderer, text, textX, textY, 0xFFFFFF);
    }

    /**
     * Renders the town ratings section with 5-star ratings for different categories.
     *
     * @param context The draw context
     * @param mouseX The mouse X position
     * @param mouseY The mouse Y position
     * @param playerTown The player's town
     * @param x The x position to start drawing
     * @param y The y position to start drawing
     * @param width The width of the rating section
     * @param height The height of the rating section
     */
    private void renderTownRatings(DrawContext context, int mouseX, int mouseY, Town playerTown, int x, int y, int width, int height) {
        // Define rating categories
        String[] categories = {
            "Structure",
            "Community",
            "Aesthetics",
            "Economy",
            "Innovation",
            "Activity"
        };

        // Define mock ratings (in a real implementation, these would come from the server)
        // Ratings are from 0 to 5, can be fractional (e.g., 3.5 stars)
        float[] ratings = {
            4.5f, // Structure
            3.0f, // Community
            5.0f, // Aesthetics
            2.5f, // Economy
            4.0f, // Innovation
            3.5f  // Activity
        };

        // Define layout parameters
        int rowHeight = 16;
        int starSize = 12; // Increased size to 12px for better visibility as requested
        int starSpacing = 4; // Fixed spacing of 4px as requested for consistency
        int categoryWidth = 65; // Width allocated for category name

        // Calculate column width based on available space
        // We want to fit two columns with some padding
        int availableWidth = width - 20; // Leave some padding on both sides
        int columnSpacing = 30; // Spacing between columns
        int columnWidth = (availableWidth - columnSpacing) / 2; // Divide available space by 2 columns

        // Calculate number of rows needed (half the categories, rounded up)
        int numRows = (categories.length + 1) / 2;

        // Calculate the total height needed for the ratings section
        int idealHeight = 22 + (numRows * rowHeight) + 20; // Title + rows + footer

        // Use the available height more efficiently - allow more space for content
        int totalHeight = Math.min(idealHeight, height - 5); // Reduced margin from 10 to 5

        // Draw a subtle background for the ratings section that fills the entire content area width
        context.fill(x, y, x + width, y + totalHeight, 0x20000000);

        // Title for the ratings section - centered
        Text titleText = Text.literal("Town Ratings").formatted(Formatting.BOLD);
        int titleWidth = this.textRenderer.getWidth(titleText);
        context.drawTextWithShadow(this.textRenderer, titleText,
            x + (width - titleWidth) / 2, y + 5, 0xFFFFFF);
        int startY = y + 22;

        // Draw a subtle separator line that spans the full width
        context.fill(x + 5, startY - 4, x + width - 5, startY - 3, 0x40FFFFFF);

        // Calculate how many categories we can fit in the available space - more lenient calculation
        int availableContentHeight = totalHeight - 25; // Reduced from 30 to 25 for more space
        int maxRows = Math.max(1, availableContentHeight / rowHeight); // Ensure at least 1 row
        int maxCategories = Math.min(categories.length, maxRows * 2); // Two columns

        // If we can't fit all categories, try to fit at least 4 (Innovation and Activity included)
        if (maxCategories < 4 && categories.length >= 4) {
            maxCategories = Math.min(4, categories.length);
        }

        for (int i = 0; i < maxCategories; i++) {
            // Determine which column and row this category belongs to
            int column = i % 2; // 0 for left column, 1 for right column
            int row = i / 2;    // Row index

            // Calculate position - evenly distribute columns across the width
            int leftColumnX = x + 10;
            int rightColumnX = x + width - columnWidth - 10;
            int categoryX = (column == 0) ? leftColumnX : rightColumnX;
            int categoryY = startY + (row * rowHeight);

            // Draw category name
            context.drawTextWithShadow(this.textRenderer, categories[i] + ":",
                categoryX, categoryY, 0xAAAAAA);

            // Calculate available width for stars
            int maxStarWidth = columnWidth - categoryWidth - 5;

            // Draw star rating
            int starsX = categoryX + categoryWidth;
            drawStarRating(context, starsX, categoryY, starSize, starSpacing, ratings[i], maxStarWidth);
        }

        // Calculate footer position (after all rows)
        int footerY = startY + (numRows * rowHeight) + 4;

        // Check if we have enough space for the footer text
        if (footerY + 12 <= y + totalHeight - 5) {
            // Draw a note about who rates the towns - centered
            Text footerText = Text.literal("Rated by server operators").formatted(Formatting.ITALIC);
            int footerWidth = this.textRenderer.getWidth(footerText);
            context.drawTextWithShadow(this.textRenderer, footerText,
                x + (width - footerWidth) / 2, footerY, 0x999999);
        }
    }

    /**
     * Draws a star rating (1-5 stars) with the specified parameters.
     *
     * @param context The draw context
     * @param x The x position to start drawing stars
     * @param y The y position to draw stars
     * @param starSize The size of each star
     * @param spacing The spacing between stars
     * @param rating The rating value (0-5, can be fractional)
     */
    private void drawStarRating(DrawContext context, int x, int y, int starSize, int spacing, float rating) {
        drawStarRating(context, x, y, starSize, spacing, rating, -1);
    }

    /**
     * Draws a star rating (1-5 stars) with the specified parameters and maximum width constraint.
     *
     * @param context The draw context
     * @param x The x position to start drawing stars
     * @param y The y position to draw stars
     * @param starSize The size of each star
     * @param spacing The spacing between stars
     * @param rating The rating value (0-5, can be fractional)
     * @param maxWidth The maximum width available for the rating display, or -1 for no constraint
     */
    private void drawStarRating(DrawContext context, int x, int y, int starSize, int spacing, float rating, int maxWidth) {
        // Colors for stars - using exact colors as requested
        int filledStarColor = 0xFFFFAA00; // Bright gold color for filled stars
        int emptyStarColor = 0xAABBBBBB; // Light gray for empty stars

        // Ensure pixel-perfect positioning
        x = Math.round(x);
        y = Math.round(y);

        // Calculate vertical offset to perfectly center stars with category text
        int verticalOffset = (this.textRenderer.fontHeight - starSize) / 2;
        int adjustedY = y + verticalOffset;

        // Calculate space needed for stars and rating text
        String ratingText = String.format("%.1f", rating);
        int ratingTextWidth = this.textRenderer.getWidth(ratingText);

        // Ensure consistent spacing between stars (3-4 pixels as requested)
        spacing = 4; // Fixed spacing of 4 pixels for consistency

        int totalStarsWidth = 5 * (starSize + spacing) - spacing; // Width of 5 stars
        int totalWidth = totalStarsWidth + 6 + ratingTextWidth; // Stars + spacing + text

        // Adjust star size if needed to fit within maxWidth while maintaining spacing
        if (maxWidth > 0 && totalWidth > maxWidth) {
            // Reduce star size while keeping spacing constant
            int availableWidthForStars = maxWidth - ratingTextWidth - 6;
            starSize = Math.max(10, (availableWidthForStars - (4 * spacing)) / 5); // Minimum size of 10px

            // Recalculate total width
            totalStarsWidth = 5 * (starSize + spacing) - spacing;
        }

        // Draw a subtle background behind the stars for better visibility
        int bgColor = 0x15000000; // Very subtle dark background
        int bgPadding = 3;
        int bgHeight = starSize + 4;
        context.fill(x - bgPadding, adjustedY - bgPadding,
                   x + totalStarsWidth + bgPadding, adjustedY + bgHeight, bgColor);

        // Save the current render state
        context.getMatrices().push();

        // Enable anti-aliasing for smoother rendering
        RenderSystem.enableBlend();
        RenderSystem.defaultBlendFunc();

        // Draw 5 stars with precise spacing and alignment
        for (int i = 0; i < 5; i++) {
            // Calculate exact position with pixel-perfect alignment
            int starX = x + i * (starSize + spacing);

            if (i < Math.floor(rating)) {
                // Full star
                drawStar(context, starX, adjustedY, starSize, filledStarColor, true);
            } else if (i == Math.floor(rating) && rating % 1 > 0) {
                // Partial star (half star)
                drawStar(context, starX, adjustedY, starSize, filledStarColor, true, rating % 1);
            } else {
                // Empty star
                drawStar(context, starX, adjustedY, starSize, emptyStarColor, false);
            }
        }

        // Draw numeric rating after the stars with improved styling
        int ratingBgColor = 0x20000000; // Subtle background for the rating text
        int ratingPadding = 3;

        // Draw background for rating text
        context.fill(x + totalStarsWidth + 4 - ratingPadding,
                   y - ratingPadding,
                   x + totalStarsWidth + 4 + ratingTextWidth + ratingPadding,
                   y + this.textRenderer.fontHeight + ratingPadding,
                   ratingBgColor);

        // Draw rating text with shadow for better visibility
        context.drawText(this.textRenderer, ratingText,
            x + totalStarsWidth + 4, y, filledStarColor, true);

        // Restore the previous render state
        RenderSystem.disableBlend();
        context.getMatrices().pop();
    }

    // Star rendering constants
    private static final int STAR_POINTS = 5; // Number of points in the star
    private static final float INNER_RADIUS_RATIO = 0.38f; // Ratio of inner to outer radius for better star shape
    private static final float STAR_SCALE = 0.85f; // Scale factor to ensure star fits within bounds

    /**
     * Draws a star shape.
     *
     * @param context The draw context
     * @param x The x position of the star
     * @param y The y position of the star
     * @param size The size of the star
     * @param color The color of the star
     * @param filled Whether the star should be filled
     */
    private void drawStar(DrawContext context, int x, int y, int size, int color, boolean filled) {
        drawStar(context, x, y, size, color, filled, 1.0f);
    }

    /**
     * Draws a star shape with partial filling.
     *
     * @param context The draw context
     * @param x The x position of the star
     * @param y The y position of the star
     * @param size The size of the star
     * @param color The color of the star
     * @param filled Whether the star should be filled
     * @param fillRatio The ratio of the star to fill (0.0-1.0)
     */
    private void drawStar(DrawContext context, int x, int y, int size, int color, boolean filled, float fillRatio) {
        // Ensure pixel-perfect positioning
        x = Math.round(x);
        y = Math.round(y);

        // Calculate shadow color (30% opacity)
        int shadowColor = (color & 0x00FFFFFF) | 0x4D000000;

        // Define empty star color - this is the standard color for empty stars
        int emptyStarColor = 0xAABBBBBB; // Light gray for empty stars

        // Save the current render state
        context.getMatrices().push();

        // Enable blending for anti-aliasing
        RenderSystem.enableBlend();
        RenderSystem.defaultBlendFunc();

        // Calculate star dimensions
        float centerX = x + size / 2.0f;
        float centerY = y + size / 2.0f;
        float outerRadius = size / 2.0f * STAR_SCALE; // Scale to ensure it fits within bounds
        float innerRadius = outerRadius * INNER_RADIUS_RATIO; // Inner radius for the star points

        // Calculate points for the star
        float[][] points = new float[STAR_POINTS * 2][2]; // Outer and inner points

        // Create a proper 5-pointed star
        for (int i = 0; i < STAR_POINTS * 2; i++) {
            // Alternate between outer and inner points
            float radius = (i % 2 == 0) ? outerRadius : innerRadius;

            // Calculate angle - rotate by -90 degrees to start at the top
            // For a 5-pointed star, we need to distribute points evenly
            float angle = (float) Math.toRadians(i * 360.0f / (STAR_POINTS * 2) - 90);

            // Calculate point coordinates with pixel-perfect positioning
            points[i][0] = centerX + radius * (float) Math.cos(angle);
            points[i][1] = centerY + radius * (float) Math.sin(angle);
        }

        // For empty stars, just draw the outline
        if (!filled) {
            // Draw shadow (1px offset with 30% opacity)
            drawStarOutline(context, points, shadowColor, 1, 1);
            // Draw main outline
            drawStarOutline(context, points, color, 0, 0);
            return;
        }

        // For fully filled stars
        if (fillRatio >= 1.0f) {
            // Draw shadow first (1px offset with 30% opacity)
            drawStarOutline(context, points, shadowColor, 1, 1);

            // Use our helper method to draw the filled star
            drawFilledStar(context, points, centerX, centerY, color, shadowColor);
            return;
        }

        // For partially filled stars
        // Special handling for half-stars
        boolean isHalfStar = Math.abs(fillRatio - 0.5f) < 0.05f;

        if (isHalfStar) {
            // IMPROVED APPROACH FOR HALF-STARS:
            // 1. Draw the empty star with uncolored outline and shadow
            // 2. Draw a colored star on top, but only the left half
            // 3. Draw the colored outline only for the left half

            // Calculate shadow color for empty star (based on emptyStarColor)
            int emptyShadowColor = (emptyStarColor & 0x00FFFFFF) | 0x4D000000;

            // Step 1: Draw the complete empty star with uncolored outline and shadow
            // This will be visible for the right half of the star
            drawStarOutline(context, points, emptyShadowColor, 1, 1);
            drawStarOutline(context, points, emptyStarColor, 0, 0);

            // Step 2: Draw the colored version on top, but only the left half
            int centerXInt = Math.round(centerX);
            context.enableScissor(x, y, centerXInt, y + size);

            // Draw the filled colored star (only left half will be visible)
            for (int i = 0; i < STAR_POINTS; i++) {
                // Get the indices of the points that form this triangle
                int p1 = i * 2;
                int p2 = (i * 2 + 1) % (STAR_POINTS * 2);
                int p3 = (i * 2 + 2) % (STAR_POINTS * 2);

                // Draw the triangle
                fillTriangle(context,
                    Math.round(centerX), Math.round(centerY),
                    Math.round(points[p1][0]), Math.round(points[p1][1]),
                    Math.round(points[p2][0]), Math.round(points[p2][1]),
                    color);

                fillTriangle(context,
                    Math.round(centerX), Math.round(centerY),
                    Math.round(points[p2][0]), Math.round(points[p2][1]),
                    Math.round(points[p3][0]), Math.round(points[p3][1]),
                    color);
            }

            // Draw the colored shadow and outline only for the left half
            drawStarOutline(context, points, shadowColor, 1, 1);
            drawStarOutline(context, points, color, 0, 0);

            context.disableScissor();
        } else {
            // For other partial fills
            // Calculate fill width proportionally
            int fillWidth = Math.round(size * fillRatio);

            // First, draw the empty star with outline
            drawStarOutline(context, points, shadowColor, 1, 1);
            drawStarOutline(context, points, color, 0, 0);

            // Now draw the filled portion by clipping
            context.enableScissor(x, y, x + fillWidth, y + size);

            // Draw just the filled triangles (no outline)
            for (int i = 0; i < STAR_POINTS; i++) {
                // Get the indices of the points that form this triangle
                int p1 = i * 2;
                int p2 = (i * 2 + 1) % (STAR_POINTS * 2);
                int p3 = (i * 2 + 2) % (STAR_POINTS * 2);

                // Draw the triangle
                fillTriangle(context,
                    Math.round(centerX), Math.round(centerY),
                    Math.round(points[p1][0]), Math.round(points[p1][1]),
                    Math.round(points[p2][0]), Math.round(points[p2][1]),
                    color);

                fillTriangle(context,
                    Math.round(centerX), Math.round(centerY),
                    Math.round(points[p2][0]), Math.round(points[p2][1]),
                    Math.round(points[p3][0]), Math.round(points[p3][1]),
                    color);
            }

            context.disableScissor();
        }

        // Restore the previous render state
        RenderSystem.disableBlend();
        context.getMatrices().pop();
    }



    /**
     * Fills a triangle with the specified color.
     *
     * @param context The draw context
     * @param x1 The x coordinate of the first point
     * @param y1 The y coordinate of the first point
     * @param x2 The x coordinate of the second point
     * @param y2 The y coordinate of the second point
     * @param x3 The x coordinate of the third point
     * @param y3 The y coordinate of the third point
     * @param color The color to fill with
     */
    private void fillTriangle(DrawContext context, int x1, int y1, int x2, int y2, int x3, int y3, int color) {
        // Simple implementation for small triangles - just fill the bounding box
        // This works well for our star rendering and avoids edge artifacts

        // Find the bounding box of the triangle
        int minX = Math.min(Math.min(x1, x2), x3);
        int minY = Math.min(Math.min(y1, y2), y3);
        int maxX = Math.max(Math.max(x1, x2), x3);
        int maxY = Math.max(Math.max(y1, y2), y3);

        // For very small triangles (which is common in our star rendering),
        // just fill the bounding box for better performance and fewer artifacts
        if (maxX - minX < 3 && maxY - minY < 3) {
            context.fill(minX, minY, maxX + 1, maxY + 1, color);
            return;
        }

        // For larger triangles, use a proper triangle filling algorithm
        // Sort the points by y-coordinate
        if (y1 > y2) {
            int temp = y1; y1 = y2; y2 = temp;
            temp = x1; x1 = x2; x2 = temp;
        }
        if (y1 > y3) {
            int temp = y1; y1 = y3; y3 = temp;
            temp = x1; x1 = x3; x3 = temp;
        }
        if (y2 > y3) {
            int temp = y2; y2 = y3; y3 = temp;
            temp = x2; x2 = x3; x3 = temp;
        }

        // Calculate slopes
        float dx1 = 0, dx2 = 0, dx3 = 0;
        if (y2 - y1 > 0) dx1 = (float)(x2 - x1) / (y2 - y1);
        if (y3 - y1 > 0) dx2 = (float)(x3 - x1) / (y3 - y1);
        if (y3 - y2 > 0) dx3 = (float)(x3 - x2) / (y3 - y2);

        // Start and end x-coordinates
        float startX, endX;

        // First half of the triangle (between y1 and y2)
        startX = endX = x1;
        if (dx1 > dx2) {
            for (int y = y1; y <= y2; y++) {
                context.fill(Math.round(startX), y, Math.round(endX) + 1, y + 1, color);
                startX += dx2;
                endX += dx1;
            }
        } else {
            for (int y = y1; y <= y2; y++) {
                context.fill(Math.round(startX), y, Math.round(endX) + 1, y + 1, color);
                startX += dx1;
                endX += dx2;
            }
        }

        // Second half of the triangle (between y2 and y3)
        startX = x2;
        endX = x1 + (y2 - y1) * dx2;
        if (dx3 > dx2) {
            for (int y = y2; y <= y3; y++) {
                context.fill(Math.round(startX), y, Math.round(endX) + 1, y + 1, color);
                startX += dx3;
                endX += dx2;
            }
        } else {
            for (int y = y2; y <= y3; y++) {
                context.fill(Math.round(startX), y, Math.round(endX) + 1, y + 1, color);
                startX += dx2;
                endX += dx3;
            }
        }
    }

    /**
     * Helper method to draw a filled star.
     *
     * @param context The draw context
     * @param points The array of points defining the star shape
     * @param centerX The x coordinate of the center of the star
     * @param centerY The y coordinate of the center of the star
     * @param color The color to fill with
     * @param shadowColor The color for the shadow
     */
    private void drawFilledStar(DrawContext context, float[][] points, float centerX, float centerY, int color, int shadowColor) {
        // Draw filled shadow first
        for (int i = 0; i < STAR_POINTS; i++) {
            // Get the indices of the points that form this triangle
            int p1 = i * 2;
            int p2 = (i * 2 + 1) % (STAR_POINTS * 2);
            int p3 = (i * 2 + 2) % (STAR_POINTS * 2);

            // Draw the triangle with shadow offset
            fillTriangle(context,
                Math.round(centerX + 1), Math.round(centerY + 1),
                Math.round(points[p1][0] + 1), Math.round(points[p1][1] + 1),
                Math.round(points[p2][0] + 1), Math.round(points[p2][1] + 1),
                shadowColor);

            fillTriangle(context,
                Math.round(centerX + 1), Math.round(centerY + 1),
                Math.round(points[p2][0] + 1), Math.round(points[p2][1] + 1),
                Math.round(points[p3][0] + 1), Math.round(points[p3][1] + 1),
                shadowColor);
        }

        // Draw main filled star
        for (int i = 0; i < STAR_POINTS; i++) {
            // Get the indices of the points that form this triangle
            int p1 = i * 2;
            int p2 = (i * 2 + 1) % (STAR_POINTS * 2);
            int p3 = (i * 2 + 2) % (STAR_POINTS * 2);

            // Draw the triangle
            fillTriangle(context,
                Math.round(centerX), Math.round(centerY),
                Math.round(points[p1][0]), Math.round(points[p1][1]),
                Math.round(points[p2][0]), Math.round(points[p2][1]),
                color);

            fillTriangle(context,
                Math.round(centerX), Math.round(centerY),
                Math.round(points[p2][0]), Math.round(points[p2][1]),
                Math.round(points[p3][0]), Math.round(points[p3][1]),
                color);
        }

        // Draw outline in the same color as the fill for a cleaner look
        drawStarOutline(context, points, color, 0, 0);
    }

    /**
     * Draws the outline of a star shape using the provided points.
     *
     * @param context The draw context
     * @param points The array of points defining the star shape
     * @param color The color to draw with
     * @param offsetX The x offset for shadow effect
     * @param offsetY The y offset for shadow effect
     */
    private void drawStarOutline(DrawContext context, float[][] points, int color, int offsetX, int offsetY) {
        // Draw lines connecting all the points to form the star outline
        for (int i = 0; i < points.length; i++) {
            int nextIndex = (i + 1) % points.length;

            // Get the coordinates of the current and next points
            int x1 = (int)(points[i][0] + offsetX);
            int y1 = (int)(points[i][1] + offsetY);
            int x2 = (int)(points[nextIndex][0] + offsetX);
            int y2 = (int)(points[nextIndex][1] + offsetY);

            // Draw a 1-pixel wide line between the points
            drawLine(context, x1, y1, x2, y2, color);
        }
    }

    /**
     * Draws a 1-pixel wide line between two points.
     *
     * @param context The draw context
     * @param x1 The x coordinate of the first point
     * @param y1 The y coordinate of the first point
     * @param x2 The x coordinate of the second point
     * @param y2 The y coordinate of the second point
     * @param color The color of the line
     */
    private void drawLine(DrawContext context, int x1, int y1, int x2, int y2, int color) {
        // Calculate the direction and length of the line
        int dx = Math.abs(x2 - x1);
        int dy = Math.abs(y2 - y1);

        // Determine which direction to step in
        int sx = x1 < x2 ? 1 : -1;
        int sy = y1 < y2 ? 1 : -1;

        int err = dx - dy;
        int e2;

        // Bresenham's line algorithm
        while (true) {
            // Draw a pixel at the current position
            context.fill(x1, y1, x1 + 1, y1 + 1, color);

            // Check if we've reached the end point
            if (x1 == x2 && y1 == y2) break;

            e2 = 2 * err;
            if (e2 > -dy) {
                err -= dy;
                x1 += sx;
            }
            if (e2 < dx) {
                err += dx;
                y1 += sy;
            }
        }
    }

    /**
     * Draws a modern button background with gradient and subtle 3D effect.
     */
    private void drawModernButton(DrawContext context, int x, int y, int width, int height, int color, boolean isHovered, boolean isActive) {
        // Extract RGB components
        int r = (color >> 16) & 0xFF;
        int g = (color >> 8) & 0xFF;
        int b = color & 0xFF;

        // Adjust colors based on state
        if (!isActive) {
            // Desaturate and darken for inactive buttons
            int avg = (r + g + b) / 3;
            r = (r + avg) / 2;
            g = (g + avg) / 2;
            b = (b + avg) / 2;
            r = r * 3/4;
            g = g * 3/4;
            b = b * 3/4;
        } else if (isHovered) {
            // Brighten for hover state
            r = Math.min(255, r + 30);
            g = Math.min(255, g + 30);
            b = Math.min(255, b + 30);
        }

        // Create colors for gradient
        int topColor = ((r) << 16) | ((g) << 8) | (b) | 0xFF000000;
        int bottomColor = ((r * 3/4) << 16) | ((g * 3/4) << 8) | (b * 3/4) | 0xFF000000;

        // Draw gradient background
        context.fillGradient(x, y, x + width, y + height, topColor, bottomColor);

        // Draw subtle 3D effect
        int highlightColor = 0x30FFFFFF; // Subtle white highlight
        int shadowColor = 0x30000000; // Subtle shadow

        // Top highlight
        context.fill(x + 1, y + 1, x + width - 1, y + 2, highlightColor);
        // Left highlight
        context.fill(x + 1, y + 1, x + 2, y + height - 1, highlightColor);

        // Bottom shadow
        context.fill(x + 2, y + height - 2, x + width - 1, y + height - 1, shadowColor);
        // Right shadow
        context.fill(x + width - 2, y + 2, x + width - 1, y + height - 2, shadowColor);
    }

    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        // Handle chat component clicks first
        if (selectedSubcategory != null && selectedSubcategory.getName().equals("Chat")) {
            // Handle emoji picker clicks with highest priority
            if (chatInputBox != null && chatInputBox.handleEmojiPickerClick(mouseX, mouseY, width, height)) {
                return true;
            }
            if (chatInputBox != null && chatInputBox.mouseClicked(mouseX, mouseY, button)) {
                return true;
            }
            if (chatDisplayArea != null && chatDisplayArea.mouseClicked(mouseX, mouseY, button)) {
                return true;
            }
        }

        // Close time dropdown if clicking outside of it (will be handled in specific click handlers)
        // This is a simplified approach - the dropdown will close when other elements are clicked

        // Get player town from client-side manager
        Town playerTown = null;
        if (client.player != null) {
            playerTown = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTown();
        }

        // Handle context menu clicks first
        if (showContextMenu && contextMenuPlayer != null) {
            if (handleContextMenuClick(mouseX, mouseY, button, playerTown)) {
                return true;
            }
            // Click outside context menu - close it
            showContextMenu = false;
            contextMenuPlayer = null;
            return true;
        }

        if (button == 0) { // Left click

            // Check if we're in the Main subcategory and clicked on the circle
            if (playerTown != null && !subcategories.isEmpty() && selectedSubcategory == subcategories.get(0)) {
                // Check if click is inside the circle
                boolean isInCircle = isPointInCircle((int)mouseX, (int)mouseY, circleCenterX, circleCenterY, circleRadius);

                if (isInCircle) {
                    // Play click sound
                    SoundUtil.playButtonClickSound();

                    // Open town image screen
                    this.client.setScreen(new TownImageScreen(this));
                    return true;
                }
            }

            if (playerTown != null) {
                // Calculate content area dimensions - EXACT same as render method
                int leftX = (width - panelWidth) / 2;
                int topY = 10;
                int headerHeight = 24; // Match the slimmer header height
                int contentX = leftX + 10;
                int contentY = topY + headerHeight + 5; // Match render method calculation
                int contentWidth = panelWidth - 20;
                int contentHeight = panelHeight - (headerHeight + 15); // Match render method calculation

                // Calculate sidebar dimensions - same as in render method
                int sidebarWidth = 90; // Reduced from 130 to 90
                int sidebarX = leftX + 5; // Moved more to the left (from contentX to leftX + 5)
                int sidebarY = contentY;
                int sidebarHeight = contentHeight;

                // Check for subcategory button clicks with scrolling support
                int subcategoryHeight = 26;
                int subcategorySpacing = 3;
                int subcategoryY = sidebarY + 5 - sidebarScrollOffset;

                for (TownSubcategory subcategory : subcategories) {
                    // Check if subcategory is visible (not scrolled out of view)
                    boolean isVisible = subcategoryY + subcategoryHeight >= sidebarY &&
                                       subcategoryY <= sidebarY + sidebarHeight;

                    if (isVisible &&
                        mouseX >= sidebarX + 5 && mouseX <= sidebarX + sidebarWidth - 10 &&
                        mouseY >= subcategoryY && mouseY <= subcategoryY + subcategoryHeight) {
                        // Play click sound
                        SoundUtil.playButtonClickSound();
                        // Select this subcategory
                        selectedSubcategory = subcategory;
                        return true;
                    }

                    // Move to next subcategory position
                    subcategoryY += subcategoryHeight + subcategorySpacing;
                }

                // Check for button clicks in the Players subcategory
                if (selectedSubcategory.getName().equals("Players")) {
                    // Use EXACT same layout calculations as in renderPlayersSubcategory
                    // Calculate content area dimensions - same as render method
                    int contentAreaX = sidebarX + sidebarWidth + 5; // Position after sidebar
                    int contentAreaWidth = leftX + panelWidth - contentAreaX - 10; // Fill remaining width

                    int leftMargin = contentAreaX + 5; // Reduced margin for more space
                    int subcategoryHeaderHeight = 32; // Taller header for better button integration
                    int headerPadding = 8;
                    int controlY = sidebarY + 5 + (subcategoryHeaderHeight - 16) / 2; // Center buttons vertically in header
                    int controlSpacing = 8;
                    int currentX = leftMargin + headerPadding;

                    // Skip search field area - it handles its own clicks
                    int searchFieldWidth = 140;
                    currentX += searchFieldWidth + controlSpacing;

                    // Sort button click detection (use icon width for consistent clicking)
                    int sortIconWidth = 24; // Icon-only width for clicking (match render)
                    int sortButtonHeight = 18; // Match render height

                    if (mouseX >= currentX && mouseX <= currentX + sortIconWidth &&
                        mouseY >= controlY && mouseY <= controlY + sortButtonHeight) {
                        // Play click sound
                        playClickSound();

                        // Cycle through sort types
                        switch (currentSortType) {
                            case RANK:
                                currentSortType = SortType.NAME;
                                break;
                            case NAME:
                                currentSortType = SortType.NEWEST;
                                break;
                            case NEWEST:
                                currentSortType = SortType.OLDEST;
                                break;
                            case OLDEST:
                                currentSortType = SortType.RANK;
                                break;
                        }

                        // Invalidate cache to force regeneration with new sort
                        cachedPlayerList = null;
                        playersScrollOffset = 0; // Reset scroll position
                        return true;
                    }
                    currentX += sortIconWidth + controlSpacing;

                    // Check filter button (cycles through options)
                    int filterIconWidth = 24; // Icon-only width for clicking (match render)
                    int filterButtonHeight = 18; // Match render height

                    if (mouseX >= currentX && mouseX <= currentX + filterIconWidth &&
                        mouseY >= controlY && mouseY <= controlY + filterButtonHeight) {
                        // Play click sound
                        playClickSound();

                        // Cycle through filter types
                        switch (currentFilterType) {
                            case ALL:
                                currentFilterType = FilterType.ONLINE;
                                break;
                            case ONLINE:
                                currentFilterType = FilterType.OFFLINE;
                                break;
                            case OFFLINE:
                                currentFilterType = FilterType.ALL;
                                break;
                        }

                        // Invalidate cache to force regeneration with new filter
                        cachedPlayerList = null;
                        playersScrollOffset = 0; // Reset scroll position
                        return true;
                    }
                    currentX += filterIconWidth + controlSpacing;

                    // Check invite button click
                    if (inviteButtonBounds != null &&
                        mouseX >= inviteButtonBounds[0] && mouseX <= inviteButtonBounds[0] + inviteButtonBounds[2] &&
                        mouseY >= inviteButtonBounds[1] && mouseY <= inviteButtonBounds[1] + inviteButtonBounds[3]) {
                        // Play click sound
                        playClickSound();

                        // Check permission before opening invite screen
                        if (playerTown != null && client.player != null) {
                            if (!PermissionChecker.checkPermissionOrShowNotification(this, playerTown, client.player.getUuid(),
                                    PermissionChecker.Permissions.PLAYER_MANAGEMENT,
                                    PermissionChecker.Permissions.CAN_INVITE_PLAYERS,
                                    PermissionChecker.FeatureNames.PLAYER_INVITATIONS)) {
                                return true; // Permission denied notification was shown
                            }
                            // Permission granted, open invite screen
                            this.client.setScreen(new InviteScreen(this, playerTown));
                        } else {
                            statusText = Text.literal("You are not in a town!").formatted(Formatting.RED);
                            statusColor = 0xFF5555; // Red
                        }

                        return true;
                    }

                    // Calculate list area dimensions using modern layout
                    int listAreaY = sidebarY + 5 + headerHeight + 5; // Same as render method
                    int listAreaHeight = sidebarHeight - (5 + headerHeight + 5) - 10; // Same as render method

                    // Scrollbar click handling removed to prevent interference with mouse wheel scrolling

                    // Check if player has permission to interact with this subcategory
                    if (!hasPermissionForSubcategory("Players", playerTown)) {
                        return false; // Don't handle clicks if no permission
                    }

                    // Check for clicks on modern player cards
                    int playerY = listAreaY - playersScrollOffset;
                    List<TownPlayer> players = createDummyPlayerList(playerTown);
                    int cardWidth = contentAreaWidth - 25; // EXACT same as in render method
                    int cardHeight = 24; // Same as in render method
                    int cardSpacing = 2; // Same as in render method

                    for (TownPlayer player : players) {
                        // Skip if player is completely outside visible area
                        if (playerY + cardHeight < listAreaY || playerY > listAreaY + listAreaHeight) {
                            playerY += cardHeight + cardSpacing;
                            continue;
                        }

                        // Check if click is on this player card
                        boolean clickOnCard = mouseX >= leftMargin + 5 && mouseX <= leftMargin + 5 + cardWidth &&
                                             mouseY >= playerY && mouseY <= playerY + cardHeight &&
                                             mouseY >= listAreaY && mouseY <= listAreaY + listAreaHeight;

                        if (clickOnCard) {
                            TownPlayerRank rank = player.getRank();

                            // Calculate button positions (same as in render method)
                            int buttonSize = 16;
                            int buttonY = playerY + (cardHeight - buttonSize) / 2;
                            int buttonStartX = leftMargin + 5 + cardWidth - (buttonSize + 35); // Updated to match render method

                            // Check info button click
                            if (mouseX >= buttonStartX && mouseX <= buttonStartX + buttonSize &&
                                mouseY >= buttonY && mouseY <= buttonY + buttonSize) {
                                // Play click sound
                                playClickSound();
                                // Open player info screen
                                this.client.setScreen(new PlayerInfoScreen(this, player, playerTown));
                                return true;
                            }

                            // Manage button removed - functionality moved to right-click context menu
                        }

                        playerY += cardHeight + cardSpacing;
                    }
                }

                // Check for button clicks in the Level subcategory
                if (selectedSubcategory.getName().equals("Level")) {
                    // No interaction needed for the placeholder message
                    // The original code is commented out for easy restoration later
                    /*
                    int contentAreaX = sidebarX + sidebarWidth + 5;
                    int leftMargin = contentAreaX + 15;

                    // Check if player has permission to interact with this subcategory
                    if (!hasPermissionForSubcategory("Level", playerTown)) {
                        return false; // Don't handle clicks if no permission
                    }
                    */
                    /* Original code commented out for easy restoration later
                    // Get town level (for demo purposes)
                    int townLevel = 3; // Example level
                    int nextLevel = townLevel + 1;
                    int rowHeight = 12; // Match the rendering code
                    int sectionSpacing = 6; // Match the rendering code

                    // Calculate the exact position of the "See All Benefits" button
                    // This should match the position in the renderLevelSubcategory method
                    int currentY = sidebarY + 8; // Start position
                    currentY += rowHeight + 2; // Level title and level info
                    currentY += 6 + 6; // Progress bar + spacing

                    // Next level benefits section
                    currentY += rowHeight; // Next level title
                    if (nextLevel <= 5) {
                        currentY += (rowHeight - 1) * 3; // 3 benefits with slight overlap
                    } else {
                        currentY += rowHeight; // Max level message
                    }

                    // "See All Benefits" button position
                    int seeAllButtonWidth = 90;
                    int seeAllButtonHeight = 14;
                    int seeAllButtonX = leftMargin;
                    int seeAllButtonY = currentY + 1;

                    if (mouseX >= seeAllButtonX && mouseX <= seeAllButtonX + seeAllButtonWidth &&
                        mouseY >= seeAllButtonY && mouseY <= seeAllButtonY + seeAllButtonHeight) {
                        // Play click sound
                        playClickSound();

                        // Check permission before opening benefits screen
                        if (!PermissionChecker.checkPermissionOrShowNotification(this, playerTown, client.player.getUuid(),
                                PermissionChecker.Permissions.TOWN_LEVEL,
                                PermissionChecker.Permissions.CAN_VIEW_BENEFITS,
                                PermissionChecker.FeatureNames.TOWN_BENEFITS)) {
                            return true; // Permission denied notification was shown
                        }

                        // Permission granted, open benefits screen
                        this.client.setScreen(new TownBenefitsScreen(this, playerTown));
                        return true;
                    }

                    // Check Contribute button
                    // Calculate the exact position of the Contribute button
                    currentY += seeAllButtonHeight + sectionSpacing - 2; // Match the spacing in the render method

                    int contributeButtonWidth = 100;
                    int contributeButtonHeight = 16;
                    int contributeButtonX = leftMargin;
                    int contributeButtonY = currentY;

                    if (mouseX >= contributeButtonX && mouseX <= contributeButtonX + contributeButtonWidth &&
                        mouseY >= contributeButtonY && mouseY <= contributeButtonY + contributeButtonHeight) {
                        // Play click sound
                        playClickSound();

                        // Open the contribution screen
                        this.client.setScreen(new TownContributeScreen(this, playerTown));
                        return true;
                    }
                    */

                    return false; // No interaction for the placeholder
                }

                // Check for button clicks in the Bank subcategory
                if (selectedSubcategory.getName().equals("Bank")) {
                    // Check if player has permission to interact with this subcategory
                    if (!hasPermissionForSubcategory("Bank", playerTown)) {
                        return false; // Don't handle clicks if no permission
                    }

                    // Use EXACT same layout calculations as in renderBankSubcategory
                    int contentAreaX = sidebarX + sidebarWidth + 5;
                    int leftMargin = contentAreaX + 5; // Reduced margin for more space
                    int bankHeaderHeight = 32; // Taller header for better button integration
                    int headerPadding = 8;
                    int controlY = sidebarY + 5 + (bankHeaderHeight - 16) / 2; // Center buttons vertically in header
                    int controlSpacing = 8;
                    int currentX = leftMargin + headerPadding;

                    // Deposit button click detection (use icon width for consistent clicking)
                    int depositIconWidth = 24; // Icon-only width for clicking (match render)
                    int depositButtonHeight = 18; // Match render height
                    if (mouseX >= currentX && mouseX <= currentX + depositIconWidth &&
                        mouseY >= controlY && mouseY <= controlY + depositButtonHeight) {
                        // Play click sound
                        playClickSound();
                        // Handle deposit action (placeholder)
                        // TODO: Open deposit dialog or handle deposit logic
                        return true;
                    }
                    currentX += depositIconWidth + controlSpacing;

                    // Withdraw button click detection
                    int withdrawIconWidth = 24; // Icon-only width for clicking (match render)
                    int withdrawButtonHeight = 18; // Match render height
                    if (mouseX >= currentX && mouseX <= currentX + withdrawIconWidth &&
                        mouseY >= controlY && mouseY <= controlY + withdrawButtonHeight) {
                        // Play click sound
                        playClickSound();
                        // Handle withdraw action (placeholder)
                        // TODO: Open withdraw dialog or handle withdraw logic
                        return true;
                    }
                    currentX += withdrawIconWidth + controlSpacing;

                    // Bank App button click detection
                    int bankAppIconWidth = 24; // Icon-only width for clicking (match render)
                    int bankAppButtonHeight = 18; // Match render height
                    if (mouseX >= currentX && mouseX <= currentX + bankAppIconWidth &&
                        mouseY >= controlY && mouseY <= controlY + bankAppButtonHeight) {
                        // Play click sound
                        playClickSound();
                        // Open bank app screen
                        this.client.setScreen(new com.pokecobble.phone.gui.BankAppScreen());
                        return true;
                    }
                    currentX += bankAppIconWidth + controlSpacing;

                    // Time dropdown button click detection (expandable button style)
                    int timeDropdownIconWidth = 24; // Icon-only width for clicking (match render)
                    int timeDropdownButtonHeight = 18; // Match render height
                    if (mouseX >= currentX && mouseX <= currentX + timeDropdownIconWidth &&
                        mouseY >= controlY && mouseY <= controlY + timeDropdownButtonHeight) {
                        // Play click sound
                        playClickSound();

                        // Cycle through time modes
                        switch (currentTimeMode) {
                            case HOUR:
                                currentTimeMode = TimeMode.DAY;
                                break;
                            case DAY:
                                currentTimeMode = TimeMode.MONTH;
                                break;
                            case MONTH:
                                currentTimeMode = TimeMode.YEAR;
                                break;
                            case YEAR:
                                currentTimeMode = TimeMode.HOUR;
                                break;
                        }

                        // Reset scroll when changing mode
                        graphScrollOffset = 0;
                        return true;
                    }

                    // Handle money graph interactions
                    if (handleMoneyGraphClick((int)mouseX, (int)mouseY, leftMargin, sidebarY, contentWidth, contentHeight)) {
                        return true;
                    }
                }

                // Check for button clicks in the Main subcategory
                if (!subcategories.isEmpty() && selectedSubcategory == subcategories.get(0)) { // Main subcategory
                    // Leave Town button has been moved to the Settings subcategory
                }

                // Check for button clicks in the Election subcategory
                if (selectedSubcategory.getName().equals("Election")) {
                    // Define content area dimensions
                    int contentAreaX = sidebarX + sidebarWidth + 5; // Position after sidebar
                    int contentAreaY = sidebarY;
                    int contentAreaWidth = leftX + panelWidth - contentAreaX - 10; // Fill remaining width
                    int contentAreaHeight = panelHeight - 35; // Same as in render method

                    // Check if player has permission to interact with this subcategory
                    if (!hasPermissionForSubcategory("Election", playerTown)) {
                        return false; // Don't handle clicks if no permission
                    }

                    // Get the election
                    Election election = ElectionManager.getInstance().getElection(playerTown);
                    if (election != null) {
                        // Layout variables
                        int leftMargin = contentAreaX + 15;
                        int rowHeight = 18; // Same as in render method
                        int currentY = contentAreaY + 10; // Same as in render method

                        // Skip header rows
                        currentY += rowHeight * 2; // Title/timer row and column headers

                        // Calculate list area dimensions
                        int listAreaY = currentY;
                        int listAreaHeight = contentAreaHeight - 20 - rowHeight - rowHeight; // Adjust height to account for title/timer and column headers

                        // Check if player has already voted
                        boolean hasVoted = false;
                        UUID votedFor = null;
                        if (client.player != null) {
                            UUID playerId = client.player.getUuid();
                            hasVoted = election.hasVoted(playerId);
                            votedFor = election.getVotes().get(playerId);
                        }

                        // Election scrollbar click handling removed to prevent interference

                        // Check for clicks on candidate vote buttons
                        if (!hasVoted) {
                            // Define column dimensions
                            int rankWidth = 50; // Same as in render method
                            int nameWidth = 130; // Same as in render method
                            int votesWidth = 45; // Same as in render method

                            // Get candidates
                            List<UUID> sortedCandidates = election.getSortedCandidates();

                            // Create a list of TownPlayer objects for the candidates
                            List<TownPlayer> candidates = new ArrayList<>();
                            for (UUID candidateId : sortedCandidates) {
                                // Get player name
                                String playerName = getPlayerName(candidateId);

                                // Get player rank
                                TownPlayerRank rank = playerTown.getPlayerRank(candidateId);
                                if (rank == null) {
                                    rank = TownPlayerRank.MEMBER; // Default to member if rank not found
                                }

                                // Create TownPlayer object
                                TownPlayer candidate = new TownPlayer(candidateId, playerName, rank, true);
                                candidates.add(candidate);
                            }

                            // Check for clicks on vote buttons
                            int candidateY = listAreaY - electionScrollOffset;

                            for (TownPlayer candidate : candidates) {
                                // Skip if candidate is completely outside visible area
                                if (candidateY + rowHeight < listAreaY || candidateY > listAreaY + listAreaHeight) {
                                    candidateY += rowHeight;
                                    continue;
                                }

                                // Check info button
                                int infoX = leftMargin + rankWidth + nameWidth + votesWidth + 5;
                                int infoY = candidateY + 1;
                                int infoButtonWidth = 30;
                                int infoButtonHeight = 16;

                                if (mouseX >= infoX && mouseX <= infoX + infoButtonWidth &&
                                    mouseY >= infoY && mouseY <= infoY + infoButtonHeight) {
                                    // Play click sound
                                    playClickSound();



                                    try {
                                        // Directly set the screen
                                        PlayerInfoScreen infoScreen = new PlayerInfoScreen(this, candidate, playerTown);
                                        this.client.setScreen(infoScreen);

                                        // Set a status message as a fallback
                                        setStatus("Click again if the player info doesn't appear", Formatting.YELLOW);
                                    } catch (Exception e) {
                                        // Log any errors
                                        System.err.println("Error opening player info screen: " + e.getMessage());
                                        e.printStackTrace();

                                        // Show an error message to the user
                                        setStatus("Error opening player info. Try again.", Formatting.RED);
                                    }

                                    return true;
                                }

                                // Check vote button
                                int voteX = infoX + infoButtonWidth + 5;
                                int voteY = candidateY + 1;
                                int voteButtonWidth = 30;
                                int voteButtonHeight = 16;

                                // Check if player has permission to vote
                                boolean canVote = false;
                                if (client.player != null && playerTown != null) {
                                    TownPlayer currentTownPlayer = playerTown.getPlayer(client.player.getUuid());
                                    if (currentTownPlayer != null) {
                                        // Everyone in the town can vote
                                        canVote = true;
                                    }
                                }

                                if (canVote && mouseX >= voteX && mouseX <= voteX + voteButtonWidth &&
                                    mouseY >= voteY && mouseY <= voteY + voteButtonHeight) {
                                    // Play click sound
                                    playClickSound();



                                    try {
                                        // Directly set the screen without using a lambda
                                        VoteConfirmationScreen confirmScreen = new VoteConfirmationScreen(this, playerTown, candidate);
                                        this.client.setScreen(confirmScreen);

                                        // Set a status message as a fallback in case the screen doesn't open
                                        setStatus("Click again if the vote confirmation doesn't appear", Formatting.YELLOW);
                                    } catch (Exception e) {
                                        // Log any errors
                                        System.err.println("Error opening vote confirmation screen: " + e.getMessage());
                                        e.printStackTrace();

                                        // Show an error message to the user
                                        setStatus("Error opening vote confirmation. Try again.", Formatting.RED);
                                    }

                                    return true;
                                }

                                candidateY += rowHeight;
                            }
                        }
                    }
                }

                // Check for button clicks in the Claims subcategory
                if (selectedSubcategory.getName().equals("Claims")) {
                    int contentAreaX = sidebarX + sidebarWidth + 5; // Position after sidebar
                    int contentAreaWidth = leftX + panelWidth - contentAreaX - 10; // Fill remaining width

                    // Check if player has permission to interact with this subcategory
                    if (!hasPermissionForSubcategory("Claims", playerTown)) {
                        return false; // Don't handle clicks if no permission
                    }

                    // Use EXACT same layout calculations as in renderClaimsSubcategory
                    int leftMargin = contentAreaX + 5; // Reduced margin for more space
                    int claimsHeaderHeight = 32; // Taller header for better button integration
                    int headerPadding = 8;
                    int controlY = sidebarY + 5 + (claimsHeaderHeight - 16) / 2; // Center buttons vertically in header
                    int controlSpacing = 8;
                    int currentX = leftMargin + headerPadding;

                    // Claim Tool button click detection (use icon width for consistent clicking)
                    int claimToolIconWidth = 24; // Icon-only width for clicking (match render)
                    int claimToolButtonHeight = 18; // Match render height

                    if (mouseX >= currentX && mouseX <= currentX + claimToolIconWidth &&
                        mouseY >= controlY && mouseY <= controlY + claimToolButtonHeight) {
                        // Play click sound
                        SoundUtil.playButtonClickSound();

                        // Check if player has permission to access claim tool with retry logic
                        if (!checkPermissionWithRetry(PermissionChecker.Permissions.CLAIM_TOOL,
                                PermissionChecker.Permissions.CAN_ACCESS_CLAIM_TOOL,
                                PermissionChecker.FeatureNames.CLAIM_TOOL)) {
                            return true; // Permission denied notification was shown or retry in progress
                        }

                        // Permission granted, activate the claim tool and close all screens
                        // Skip permission check since we already verified it with retry logic
                        ClaimTool.getInstance().activate(playerTown, true);
                        // No need to call this.close() as ClaimTool.activate() already closes all screens
                        return true;
                    }
                    currentX += claimToolIconWidth + controlSpacing;

                    // View Boundaries button click detection
                    int viewBoundariesIconWidth = 24; // Icon-only width for clicking (match render)
                    int viewBoundariesButtonHeight = 18; // Match render height

                    if (mouseX >= currentX && mouseX <= currentX + viewBoundariesIconWidth &&
                        mouseY >= controlY && mouseY <= controlY + viewBoundariesButtonHeight) {
                        // Play click sound
                        playClickSound();
                        // Handle view boundaries action (placeholder)
                        return true;
                    }
                    currentX += viewBoundariesIconWidth + controlSpacing;

                    // Tag Settings button click detection
                    int tagSettingsIconWidth = 24; // Icon-only width for clicking (match render)
                    int tagSettingsButtonHeight = 18; // Match render height

                    if (mouseX >= currentX && mouseX <= currentX + tagSettingsIconWidth &&
                        mouseY >= controlY && mouseY <= controlY + tagSettingsButtonHeight) {
                        // Play click sound
                        SoundUtil.playButtonClickSound();

                        // Check if player has permission to access claim tool with retry logic
                        if (!checkPermissionWithRetry(PermissionChecker.Permissions.CLAIM_TOOL,
                                PermissionChecker.Permissions.CAN_ACCESS_CLAIM_TOOL,
                                PermissionChecker.FeatureNames.CLAIM_TOOL)) {
                            return true; // Permission denied notification was shown or retry in progress
                        }

                        // Permission granted, open the claim tag screen with this screen as parent
                        this.client.setScreen(new com.pokecobble.town.gui.ClaimTagScreen(this, playerTown));
                        return true;
                    }

                    // "See More" button click detection (in claim history header)
                    // Calculate history area position to match rendering
                    int historyAreaY = sidebarY + 5 + claimsHeaderHeight + 5; // Match rendering calculation
                    int historyHeaderHeight = 24; // Match rendering

                    // Position button in right side of header (match rendering calculation)
                    int seeMoreIconWidth = 24;
                    int seeMoreButtonHeight = 18;
                    int seeMoreX = leftMargin + (contentAreaWidth - 10) - seeMoreIconWidth - 10; // Right aligned with margin
                    int seeMoreY = historyAreaY + (historyHeaderHeight - seeMoreButtonHeight) / 2; // Center vertically in header

                    if (mouseX >= seeMoreX && mouseX <= seeMoreX + seeMoreIconWidth &&
                        mouseY >= seeMoreY && mouseY <= seeMoreY + seeMoreButtonHeight) {
                        // Play click sound
                        SoundUtil.playButtonClickSound();

                        // No separate history screen - all history is shown in the claims subcategory
                        // User can scroll to see more history entries
                        return true;
                    }
                }

                // Check for button clicks in the Jobs subcategory
                if (selectedSubcategory.getName().equals("Jobs")) {
                    // Check for jobs management button click
                    if (mouseX >= jobsButtonX && mouseX <= jobsButtonX + jobsButtonWidth &&
                        mouseY >= jobsButtonY && mouseY <= jobsButtonY + jobsButtonHeight) {
                        // Play click sound
                        SoundUtil.playButtonClickSound();

                        // Open the new JobsScreen
                        this.client.setScreen(new com.pokecobble.town.gui.JobsScreen(this, playerTown));
                        return true;
                    }
                }

                // Check for button clicks in the Settings subcategory
                if (selectedSubcategory.getName().equals("Settings")) {
                    // Calculate content area dimensions to match rendering exactly
                    int settingsContentX = sidebarX + sidebarWidth + 5;
                    int settingsContentY = sidebarY;
                    int settingsContentWidth = leftX + panelWidth - settingsContentX - 10;
                    int settingsContentHeight = panelHeight - 35;

                    // Match the exact rendering calculations from renderSettingsSubcategory
                    int settingsLeftMargin = settingsContentX + 5; // Match rendering: contentX + 5
                    int settingsHeaderHeight = 32; // Taller header for better button integration
                    int currentY = settingsContentY + 5; // Match rendering: contentY + 5

                    // Check if player has permission to interact with this subcategory
                    if (!hasPermissionForSubcategory("Settings", playerTown)) {
                        return false; // Don't handle clicks if no permission
                    }

                    // Header controls - match rendering exactly
                    int headerPadding = 8;
                    int controlY = currentY + (headerHeight - 16) / 2; // Center controls vertically in header
                    int controlSpacing = 8;
                    int currentX = settingsLeftMargin + headerPadding;

                    // Header now only contains the Leave Town button

                    // Leave Town button (expandable)
                    int leaveIconWidth = 24;
                    if (mouseX >= currentX && mouseX <= currentX + leaveIconWidth &&
                        mouseY >= controlY && mouseY <= controlY + 18) {
                        // Play click sound
                        SoundUtil.playButtonClickSound();

                        // Check if player is mayor
                        boolean isMayor = false;
                        if (client.player != null) {
                            UUID playerId = client.player.getUuid();
                            isMayor = playerTown.getPlayerRank(playerId) == TownPlayerRank.OWNER;
                        }

                        // Show leave town confirmation screen
                        this.client.setScreen(new LeaveTownConfirmationScreen(this, playerTown, isMayor));
                        return true;
                    }

                    // Zone click handling - horizontal layout (side by side)
                    currentY += headerHeight + 10;
                    int contentAreaY = currentY;
                    int availableHeight = Math.max(140, settingsContentY + settingsContentHeight - contentAreaY - 5);
                    int zoneSpacing = 10; // Horizontal spacing between zones
                    int availableWidth = settingsContentWidth - 10; // Total available width for zones
                    int zoneWidth = (availableWidth - zoneSpacing) / 2; // Split width into two zones with spacing
                    int zoneHeight = availableHeight; // Use full available height for both zones

                    // Town Settings Zone clicks (left side)
                    if (handleTownSettingsZoneClick(mouseX, mouseY, playerTown, settingsLeftMargin, contentAreaY, zoneWidth, zoneHeight)) {
                        return true;
                    }

                    // Player Settings Zone clicks (right side)
                    int playerSettingsX = settingsLeftMargin + zoneWidth + zoneSpacing;
                    if (handlePlayerSettingsZoneClick(mouseX, mouseY, playerTown, playerSettingsX, contentAreaY, zoneWidth, zoneHeight)) {
                        return true;
                    }
                }
            }
        } else if (button == 1) { // Right click
            // Handle right-click for player context menu
            if (playerTown != null && selectedSubcategory != null && selectedSubcategory.getName().equals("Players")) {
                // Use EXACT same layout calculations as in left-click handling
                int leftX = (width - panelWidth) / 2;
                int topY = 10;
                int headerHeight = 24;
                int contentX = leftX + 10;
                int contentY = topY + headerHeight + 5;
                int contentWidth = panelWidth - 20;
                int contentHeight = panelHeight - (headerHeight + 15);

                // Calculate sidebar dimensions - same as in left-click handling
                int sidebarWidth = 90;
                int sidebarX = leftX + 5;
                int sidebarY = contentY;
                int sidebarHeight = contentHeight;

                // Calculate content area dimensions - same as left-click handling
                int contentAreaX = sidebarX + sidebarWidth + 5;
                int contentAreaWidth = leftX + panelWidth - contentAreaX - 10;

                int leftMargin = contentAreaX + 5;
                int subcategoryHeaderHeight = 32;

                // Calculate list area dimensions using modern layout - same as left-click handling
                int listAreaY = sidebarY + 5 + subcategoryHeaderHeight + 5;
                int listAreaHeight = sidebarHeight - (5 + subcategoryHeaderHeight + 5) - 10;

                // Get filtered and sorted players
                List<TownPlayer> players = createDummyPlayerList(playerTown);
                if (players.isEmpty()) return super.mouseClicked(mouseX, mouseY, button);

                // Calculate player card dimensions - same as left-click handling
                int cardWidth = contentAreaWidth - 25;
                int cardHeight = 24;
                int cardSpacing = 2;
                int playerY = listAreaY - playersScrollOffset;

                for (TownPlayer player : players) {
                    // Skip if player is completely outside visible area
                    if (playerY + cardHeight < listAreaY || playerY > listAreaY + listAreaHeight) {
                        playerY += cardHeight + cardSpacing;
                        continue;
                    }

                    // Check if right-click is on this player card - EXACT same as left-click handling
                    boolean clickOnCard = mouseX >= leftMargin + 5 && mouseX <= leftMargin + 5 + cardWidth &&
                                         mouseY >= playerY && mouseY <= playerY + cardHeight &&
                                         mouseY >= listAreaY && mouseY <= listAreaY + listAreaHeight;

                    if (clickOnCard) {
                        // Show context menu for this player
                        showContextMenu = true;
                        contextMenuX = (int)mouseX;
                        contextMenuY = (int)mouseY;
                        contextMenuPlayer = player;

                        // Clear rank name hover when context menu opens
                        hoveredPlayerUUID = null;

                        playClickSound();
                        return true;
                    }

                    playerY += cardHeight + cardSpacing;
                }
            }
        }

        return super.mouseClicked(mouseX, mouseY, button);
    }

    @Override
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        // Handle chat input box key presses first
        if (selectedSubcategory != null && selectedSubcategory.getName().equals("Chat")) {
            if (chatInputBox != null && chatInputBox.keyPressed(keyCode, scanCode, modifiers)) {
                return true;
            }
        }

        // Close context menu on ESC key
        if (keyCode == 256 && showContextMenu) { // 256 is ESC key
            showContextMenu = false;
            contextMenuPlayer = null;
            return true;
        }
        return super.keyPressed(keyCode, scanCode, modifiers);
    }

    @Override
    public boolean charTyped(char chr, int modifiers) {
        // Handle chat input box character input first
        if (selectedSubcategory != null && selectedSubcategory.getName().equals("Chat")) {
            if (chatInputBox != null && chatInputBox.charTyped(chr, modifiers)) {
                return true;
            }
        }

        return super.charTyped(chr, modifiers);
    }

    @Override
    public void mouseMoved(double mouseX, double mouseY) {
        // If context menu is active, don't pass mouse events to underlying UI
        if (showContextMenu && contextMenuPlayer != null) {
            return; // Block all mouse movement events when context menu is active
        }
        super.mouseMoved(mouseX, mouseY);
    }

    /**
     * Handles money graph click interactions including time mode buttons and drag start.
     */
    private boolean handleMoneyGraphClick(int mouseX, int mouseY, int leftMargin, int sidebarY, int contentWidth, int contentHeight) {
        // Calculate graph area using the same logic as renderBankSubcategory
        int balanceY = sidebarY + 5;
        int balanceCardHeight = 35;
        int graphAreaY = balanceY + balanceCardHeight + 8; // Updated to match rendering
        int availableHeight = sidebarY + contentHeight - graphAreaY - 5; // Updated to match rendering
        int graphAreaHeight = Math.max(120, availableHeight); // Updated to match rendering

        int graphX = leftMargin;
        int graphY = graphAreaY;
        int graphWidth = contentWidth; // Updated to match rendering - no margins
        int graphHeight = graphAreaHeight;

        // Ensure graph fits within content bounds
        if (graphY + graphHeight > sidebarY + contentHeight - 5) {
            graphHeight = sidebarY + contentHeight - graphY - 5;
        }

        // Check if click is within graph area
        if (mouseX < graphX || mouseX > graphX + graphWidth || mouseY < graphY || mouseY > graphY + graphHeight) {
            return false;
        }

        // Time mode selection now handled in header dropdown - no separate buttons

        // Check if click is in the chart area for drag start - header moved outside
        int chartY = graphY; // No padding since we removed it
        int chartHeight = Math.max(50, graphHeight - 5);

        if (mouseY >= chartY && mouseY <= chartY + chartHeight) {
            // Start dragging
            isDragging = true;
            lastDragX = mouseX;
            return true;
        }

        return false;
    }

    @Override
    public boolean mouseDragged(double mouseX, double mouseY, int button, double deltaX, double deltaY) {
        // Handle chat display area dragging first
        if (selectedSubcategory != null && selectedSubcategory.getName().equals("Chat")) {
            if (chatDisplayArea != null && chatDisplayArea.mouseDragged(mouseX, mouseY, button, deltaX, deltaY)) {
                return true;
            }
        }

        // If context menu is active, block all dragging
        if (showContextMenu && contextMenuPlayer != null) {
            return true; // Block dragging when context menu is active
        }

        if (isDragging && button == 0) {
            // Handle graph scrolling via drag
            int dragDelta = (int)(mouseX - lastDragX);
            int sensitivity = 2; // Adjust scroll sensitivity

            if (Math.abs(dragDelta) > sensitivity) {
                // Scroll based on drag direction
                int scrollChange = -dragDelta / sensitivity;
                graphScrollOffset = Math.max(0, Math.min(maxScrollOffset, graphScrollOffset + scrollChange));
                lastDragX = (int)mouseX;
            }
            return true;
        }
        return super.mouseDragged(mouseX, mouseY, button, deltaX, deltaY);
    }

    @Override
    public boolean mouseReleased(double mouseX, double mouseY, int button) {
        // Handle chat display area mouse release first
        if (selectedSubcategory != null && selectedSubcategory.getName().equals("Chat")) {
            if (chatDisplayArea != null && chatDisplayArea.mouseReleased(mouseX, mouseY, button)) {
                return true;
            }
        }

        // If context menu is active, block all mouse releases
        if (showContextMenu && contextMenuPlayer != null) {
            return true; // Block mouse release when context menu is active
        }

        if (button == 0) {
            isDragging = false;
        }
        return super.mouseReleased(mouseX, mouseY, button);
    }

    @Override
    public boolean mouseScrolled(double mouseX, double mouseY, double amount) {
        // Handle chat display area scrolling first
        if (selectedSubcategory != null && selectedSubcategory.getName().equals("Chat")) {
            if (chatDisplayArea != null && chatDisplayArea.mouseScrolled(mouseX, mouseY, amount)) {
                return true;
            }
        }

        // If context menu is active, block all scrolling
        if (showContextMenu && contextMenuPlayer != null) {
            return true; // Block scrolling when context menu is active
        }

        // Handle sidebar scrolling
        int leftX = (width - panelWidth) / 2;
        int topY = 10;
        int headerHeight = 24; // Match the slimmer header height
        int contentY = topY + headerHeight + 5;
        int sidebarWidth = 90;
        int sidebarX = leftX + 5;
        int sidebarY = contentY;
        int sidebarHeight = panelHeight - (headerHeight + 15);

        if (mouseX >= sidebarX && mouseX <= sidebarX + sidebarWidth &&
            mouseY >= sidebarY && mouseY <= sidebarY + sidebarHeight) {
            // Scroll the sidebar
            sidebarScrollOffset -= (int) (amount * 20);
            sidebarScrollOffset = Math.max(0, sidebarScrollOffset);
            return true;
        }

        // Handle money graph scrolling in Bank subcategory
        if (selectedSubcategory != null && selectedSubcategory.getName().equals("Bank")) {
            // Calculate graph area using the same logic as renderBankSubcategory
            int contentAreaX = leftX + 5 + sidebarWidth + 5;
            int leftMargin = contentAreaX + 5;
            int bankSidebarY = 35;

            int balanceY = bankSidebarY + 5;
            int balanceCardHeight = 35;
            int graphAreaY = balanceY + balanceCardHeight + 8; // Updated to match rendering
            int contentHeight = panelHeight - 35;
            int availableHeight = bankSidebarY + contentHeight - graphAreaY - 5; // Updated to match rendering
            int graphAreaHeight = Math.max(120, availableHeight); // Updated to match rendering

            int graphX = leftMargin;
            int graphY = graphAreaY;
            int graphWidth = panelWidth - 110; // Further increased for maximum space
            int graphHeight = graphAreaHeight;

            // Ensure graph fits within content bounds
            if (graphY + graphHeight > bankSidebarY + contentHeight - 10) {
                graphHeight = bankSidebarY + contentHeight - graphY - 10;
            }

            if (mouseX >= graphX && mouseX <= graphX + graphWidth &&
                mouseY >= graphY && mouseY <= graphY + graphHeight) {
                // Scroll the graph
                graphScrollOffset -= (int) (amount * 3); // Scroll 3 data points at a time
                graphScrollOffset = Math.max(0, Math.min(maxScrollOffset, graphScrollOffset));
                return true;
            }
        }

        // Simple player list scrolling - only handle Players subcategory
        if (selectedSubcategory != null && selectedSubcategory.getName().equals("Players")) {
            // Simple scroll - just update the offset and apply bounds
            playersScrollOffset -= (int) (amount * 20);

            // Apply basic bounds (detailed bounds will be handled in render method)
            playersScrollOffset = Math.max(0, playersScrollOffset);

            return true;
        }

        // Handle Settings subcategory scrolling
        if (selectedSubcategory != null && selectedSubcategory.getName().equals("Settings")) {
            // Calculate settings content area
            int settingsContentX = leftX + 5 + sidebarWidth + 5;
            int settingsContentY = contentY;
            int settingsContentWidth = panelWidth - (5 + sidebarWidth + 5) - 10;
            int settingsContentHeight = panelHeight - (headerHeight + 15) - 35;

            int settingsLeftMargin = settingsContentX + 5;
            int settingsHeaderHeight = 32;
            int currentY = settingsContentY + 5;
            currentY += settingsHeaderHeight + 10;
            int contentAreaY = currentY;
            int availableHeight = Math.max(140, settingsContentY + settingsContentHeight - contentAreaY - 5);
            int zoneSpacing = 10; // Horizontal spacing between zones
            int availableWidth = settingsContentWidth - 10; // Total available width for zones
            int zoneWidth = (availableWidth - zoneSpacing) / 2; // Split width into two zones with spacing
            int zoneHeight = availableHeight; // Use full available height for both zones

            // Town Settings Zone scrolling (left side)
            int townSettingsZoneX = settingsLeftMargin;
            int townSettingsZoneY = contentAreaY;

            if (mouseX >= townSettingsZoneX && mouseX <= townSettingsZoneX + zoneWidth &&
                mouseY >= townSettingsZoneY && mouseY <= townSettingsZoneY + zoneHeight) {

                // Calculate max scroll for town settings
                int townSettingsContentHeight = 3 * (24 + 6) - 6; // 3 buttons with spacing
                int townSettingsMaxScroll = Math.max(0, townSettingsContentHeight - (zoneHeight - 28));

                townSettingsScrollOffset -= (int) (amount * 20);
                townSettingsScrollOffset = Math.max(0, Math.min(townSettingsScrollOffset, townSettingsMaxScroll));
                return true;
            }

            // Player Settings Zone scrolling (right side)
            int playerSettingsZoneX = settingsLeftMargin + zoneWidth + zoneSpacing;

            if (mouseX >= playerSettingsZoneX && mouseX <= playerSettingsZoneX + zoneWidth &&
                mouseY >= townSettingsZoneY && mouseY <= townSettingsZoneY + zoneHeight) {

                // Calculate max scroll for player settings
                int playerSettingsContentHeight = 4 * (24 + 6) - 6; // 4 buttons with spacing
                int playerSettingsMaxScroll = Math.max(0, playerSettingsContentHeight - (zoneHeight - 28));

                playerSettingsScrollOffset -= (int) (amount * 20);
                playerSettingsScrollOffset = Math.max(0, Math.min(playerSettingsScrollOffset, playerSettingsMaxScroll));
                return true;
            }
        }

        // Handle Claims subcategory scrolling - simplified like Players
        if (selectedSubcategory != null && selectedSubcategory.getName().equals("Claims")) {
            // Simple scroll - just update the offset and apply bounds
            claimsHistoryScrollOffset -= (int) (amount * 20);

            // Apply basic bounds (detailed bounds will be handled in render method)
            claimsHistoryScrollOffset = Math.max(0, claimsHistoryScrollOffset);

            return true;
        }

        // Handle Jobs subcategory scrolling
        if (selectedSubcategory != null && selectedSubcategory.getName().equals("Jobs")) {
            // Calculate jobs content area using same logic as renderJobsSubcategory
            int contentAreaX = leftX + 5 + sidebarWidth + 5;
            int contentAreaWidth = panelWidth - (5 + sidebarWidth + 5) - 10;
            int leftMargin = contentAreaX + 5;
            int jobsHeaderHeight = 32;
            int currentY = contentY + 5;
            currentY += jobsHeaderHeight + 5; // Small gap after header
            int listAreaY = currentY;
            int listAreaHeight = sidebarHeight - (currentY - contentY) - 10; // Use remaining space with small bottom margin

            // Check if mouse is in list area
            if (mouseX >= leftMargin + 5 && mouseX <= leftMargin + contentAreaWidth - 20 &&
                mouseY >= listAreaY && mouseY <= listAreaY + listAreaHeight) {

                // Get jobs data for scroll calculation
                com.pokecobble.town.client.ClientTownJobsManager jobsManager = com.pokecobble.town.client.ClientTownJobsManager.getInstance();
                if (jobsManager.hasJobsData()) {
                    java.util.List<com.pokecobble.town.TownJob.JobType> allJobs = getFilteredJobList(jobsManager);

                    // Calculate total height using dynamic card dimensions (accounts for expanded cards)
                    int totalHeight = calculateJobsContentHeightCompact(allJobs);
                    if (allJobs.size() > 0) {
                        totalHeight += (allJobs.size() - 1) * 2; // Add spacing between cards (2px spacing)
                    }
                    int maxScrollOffset = Math.max(0, totalHeight - listAreaHeight);

                    // Apply scrolling
                    jobsScrollOffset -= (int) (amount * 30); // Scroll speed
                    jobsScrollOffset = Math.max(0, Math.min(jobsScrollOffset, maxScrollOffset));
                    return true;
                }
            }
        }

        return super.mouseScrolled(mouseX, mouseY, amount);
    }

    /**
     * Called when the search field text changes.
     */
    private void onSearchFieldChanged(String newText) {
        searchQuery = newText.trim();
        // Invalidate cache to force regeneration with new search query
        cachedPlayerList = null;
        // Reset scroll position when search changes
        playersScrollOffset = 0;
    }

    /**
     * Sets the status text.
     *
     * @param message The message to display
     * @param formatting The formatting to apply
     */
    public void setStatus(String message, Formatting formatting) {
        statusText = Text.literal(message).formatted(formatting);

        // Set color based on formatting
        if (formatting == Formatting.RED) {
            statusColor = 0xFF5555; // Red
        } else if (formatting == Formatting.GREEN) {
            statusColor = 0x55FF55; // Green
        } else if (formatting == Formatting.YELLOW || formatting == Formatting.GOLD) {
            statusColor = 0xFFFF55; // Yellow
        } else {
            statusColor = 0xFFFFFF; // White
        }
    }

    // openClaimHistoryScreen method removed - claim history is now shown directly in the claims subcategory

    /**
     * Renders a placeholder message for the Level subcategory.
     * This is a temporary implementation that will be replaced with the actual functionality in a future update.
     *
     * @param context The draw context
     * @param contentX The X position of the content area
     * @param contentY The Y position of the content area
     * @param contentWidth The width of the content area
     * @param contentHeight The height of the content area
     */
    private void renderLevelPlaceholderMessage(DrawContext context, int contentX, int contentY, int contentWidth, int contentHeight) {
        // Calculate usable area with margins
        int margin = 20;
        int usableX = contentX + margin;
        int usableY = contentY + margin;
        int usableWidth = contentWidth - (margin * 2);
        int usableHeight = contentHeight - (margin * 2);

        // Draw a nice background
        context.fill(usableX, usableY, usableX + usableWidth, usableY + usableHeight, 0x30000000);

        // Draw title
        context.drawCenteredTextWithShadow(this.textRenderer, Text.literal("Town Level System").formatted(Formatting.BOLD, Formatting.GOLD),
            usableX + usableWidth / 2, usableY + 20, 0xFFFFAA00);

        // Draw coming soon message
        context.drawCenteredTextWithShadow(this.textRenderer, Text.literal("This feature will be available in a future update!").formatted(Formatting.ITALIC),
            usableX + usableWidth / 2, usableY + 50, 0xFFFFFF);

        // Draw feature description - shorter lines that fit within the content area
        String[] description = {
            "The Town Level system will allow towns to",
            "gain experience and level up through",
            "various activities and challenges.",
            "",
            "Each level will unlock new benefits such as",
            "increased claim limits and special structures."
        };

        // Calculate vertical spacing to ensure text fits
        int totalTextHeight = description.length * 12; // 12 pixels per line
        int startY = usableY + (usableHeight / 2); // Start from the middle

        // Draw each line of the description
        int descY = startY;
        for (String line : description) {
            context.drawCenteredTextWithShadow(this.textRenderer, line,
                usableX + usableWidth / 2, descY, 0xFFCCCCCC);
            descY += 12;
        }
    }

    /**
     * Refreshes the player list to ensure ranks are up to date.
     * This should be called when the election status changes or when town player data is updated.
     */
    public void refreshPlayerList() {
        // Clear the cached player list to force a refresh
        lastTownId = null;
        cachedPlayerList = null;

        // Reset scroll positions
        playersScrollOffset = 0;
        electionScrollOffset = 0;
        sidebarScrollOffset = 0;
    }

    /**
     * Implements RefreshableComponent interface.
     */
    @Override
    public void refresh() {
        refreshPlayerList();

        // Simple refresh like TownImageScreen - no complex protection logic needed

        // Request fresh data from server
        TownNetworkHandler.requestTownData();

        // Also request fresh image selection data
        Town playerTown = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTown();
        if (playerTown != null) {
            com.pokecobble.town.client.TownImageSelectionClient.requestSelectionData(playerTown.getId());
        }
    }

    /**
     * Refreshes the claims subcategory data including claim history and claim counter.
     * This is more efficient than refreshing the entire screen when only claim data has changed.
     */
    public void refreshClaimsData() {
        // Request claim history and claim count from server using simple sync system
        if (client.player != null) {
            Town playerTown = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTown();
            if (playerTown != null) {
                // Request claim history from server
                com.pokecobble.town.network.SimpleClaimHistorySync.requestClaimHistory(playerTown.getId());
                // Request claim count from server (using same pattern as claim history)
                com.pokecobble.town.network.SimpleClaimCountSync.requestClaimCount(playerTown.getId());
            }
        }
    }

    /**
     * Gets the data types this component depends on.
     */
    @Override
    public java.util.Set<String> getDependentDataTypes() {
        return java.util.Set.of("town_data", "player_data", "election_data", "chunk_data");
    }

    /**
     * Called when town player data is updated.
     * Implements the TownPlayerUpdateCallback interface.
     *
     * @param townId The ID of the town that was updated
     */
    @Override
    public void onTownPlayerUpdate(UUID townId) {
        // Check if this is the player's town
        if (client.player != null) {
            Town playerTown = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTown();
            if (playerTown != null && playerTown.getId().equals(townId)) {
                // Refresh the player list
                refreshPlayerList();

                // Simple image handling - no complex loading needed like TownImageScreen
            }
        }
    }

    /**
     * Called when election data is updated.
     * Implements the ElectionUpdateCallback interface.
     *
     * @param townId The ID of the town whose election was updated
     */
    @Override
    public void onElectionUpdate(UUID townId) {
        // Check if this is the player's town
        if (client.player != null) {
            Town playerTown = TownManager.getInstance().getPlayerTown(client.player.getUuid());
            if (playerTown != null && playerTown.getId().equals(townId)) {
                // Refresh the election subcategory by refreshing all subcategories
                setupSubcategories();
                // Reset election scroll offset
                electionScrollOffset = 0;
            }
        }
    }

    /**
     * Called when player data is updated (including rank changes).
     * Handles player data update events from the consolidated sync system.
     */
    private void onPlayerDataUpdated(Object eventData) {
        if (eventData instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> data = (Map<String, Object>) eventData;

            // Refresh the player list when any player data is updated
            refreshPlayerList();
        }
    }

    private void onTownListUpdated(Object eventData) {
        // Refresh the player list when town list is updated (includes rank changes)
        refreshPlayerList();
        com.pokecobble.Pokecobbleclaim.LOGGER.debug("MyTownScreen refreshed due to town list update");
    }

    /**
     * Called when town data is updated (including claim history and claim count).
     * Handles town data update events from the consolidated sync system.
     */
    private void onTownDataUpdated(Object eventData) {
        if (eventData instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> data = (Map<String, Object>) eventData;

            // Check if this update is for the player's town
            Object townIdObj = data.get("townId");
            if (townIdObj instanceof UUID) {
                UUID updatedTownId = (UUID) townIdObj;

                if (client.player != null) {
                    Town playerTown = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTown();
                    if (playerTown != null && playerTown.getId().equals(updatedTownId)) {
                        // Refresh the player list when town data is updated
                        refreshPlayerList();

                        // Log claim count for debugging
                        Pokecobbleclaim.LOGGER.debug("MyTownScreen: Town data updated - claim count: {}/{}",
                            playerTown.getClaimCount(), playerTown.getMaxClaims());

                        // The claim history data is already updated in the town object,
                        // so we just need to ensure the UI reflects the changes
                        // No need to request data again since it was just synchronized

                        Pokecobbleclaim.LOGGER.debug("MyTownScreen: Town data updated for town {} - refreshing UI (claim history should now be visible)", updatedTownId);
                    }
                }
            }
        }
    }

    /**
     * Called when chunk data is updated (including claim operations).
     * Handles chunk data update events from the consolidated sync system.
     */
    private void onChunkDataUpdated(Object eventData) {
        if (client.player != null) {
            Town playerTown = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTown();
            if (playerTown != null) {
                // Refresh claims data when chunk data is updated
                // This will trigger when chunks are claimed, unclaimed, or tags are modified
                refreshClaimsData();
                Pokecobbleclaim.LOGGER.debug("MyTownScreen refreshed claims data due to chunk data update");
            }
        }
    }

    /**
     * Updates bank button animations.
     */
    private void updateBankButtonAnimations() {
        float animationSpeed = 0.15f;

        // Update deposit button animation
        if (depositButtonHovered) {
            depositButtonAnimation = Math.min(1.0f, depositButtonAnimation + animationSpeed);
        } else {
            depositButtonAnimation = Math.max(0.0f, depositButtonAnimation - animationSpeed);
        }

        // Update withdraw button animation
        if (withdrawButtonHovered) {
            withdrawButtonAnimation = Math.min(1.0f, withdrawButtonAnimation + animationSpeed);
        } else {
            withdrawButtonAnimation = Math.max(0.0f, withdrawButtonAnimation - animationSpeed);
        }

        // Update bank app button animation
        if (bankAppButtonHovered) {
            bankAppButtonAnimation = Math.min(1.0f, bankAppButtonAnimation + animationSpeed);
        } else {
            bankAppButtonAnimation = Math.max(0.0f, bankAppButtonAnimation - animationSpeed);
        }

        // Update time dropdown button animation
        if (timeDropdownButtonHovered) {
            timeDropdownButtonAnimation = Math.min(1.0f, timeDropdownButtonAnimation + animationSpeed);
        } else {
            timeDropdownButtonAnimation = Math.max(0.0f, timeDropdownButtonAnimation - animationSpeed);
        }
    }

    /**
     * Opens the building request selection screen.
     */
    private void openBuildingRequestScreen() {
        // Get player town from client-side manager
        Town playerTown = null;
        if (client.player != null) {
            playerTown = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTown();
        }

        if (playerTown == null) {
            NotificationManager.getInstance().addErrorNotification("You must be in a town to request building approval");
            return;
        }

        // Create a simple building selection screen
        this.client.setScreen(new BuildingRequestSelectionScreen(this, playerTown));
    }

    /**
     * Updates jobs button animations.
     */
    private void updateJobsButtonAnimations() {
        float animationSpeed = 0.15f;

        // Update refresh jobs button animation
        if (refreshJobsButtonHovered) {
            refreshJobsButtonAnimation = Math.min(1.0f, refreshJobsButtonAnimation + animationSpeed);
        } else {
            refreshJobsButtonAnimation = Math.max(0.0f, refreshJobsButtonAnimation - animationSpeed);
        }

        // Update income display button animation
        if (incomeDisplayButtonHovered) {
            incomeDisplayButtonAnimation = Math.min(1.0f, incomeDisplayButtonAnimation + animationSpeed);
        } else {
            incomeDisplayButtonAnimation = Math.max(0.0f, incomeDisplayButtonAnimation - animationSpeed);
        }
    }

    /**
     * Checks if mouse is over an expandable button.
     */
    private boolean isMouseOverExpandableButton(int mouseX, int mouseY, int buttonX, int buttonY, float animation, String icon, String text) {
        int iconWidth = 24; // Standard icon width
        int buttonHeight = 18; // Standard button height
        return mouseX >= buttonX && mouseX <= buttonX + iconWidth &&
               mouseY >= buttonY && mouseY <= buttonY + buttonHeight;
    }

    /**
     * Draws the bank balance card and returns the new X position.
     */
    private int drawBankBalance(DrawContext context, int x, int y, int width) {
        int height = 28; // Original height to match original styling

        // Get actual balance (placeholder for now)
        int balance = 1000; // TODO: Get from actual bank system

        // Draw balance card background with original styling
        context.fill(x, y, x + width, y + height, 0x40000000);
        context.fill(x, y, x + width, y + 1, 0x40FFFFFF);
        context.fill(x, y, x + 1, y + height, 0x40FFFFFF);

        // Draw balance text with original styling
        String balanceText = "Bank: $" + String.format("%,d", balance);
        int balanceTextWidth = this.textRenderer.getWidth(balanceText);
        context.drawTextWithShadow(this.textRenderer, Text.literal(balanceText).formatted(Formatting.BOLD),
            x + (width - balanceTextWidth) / 2, y + 10, 0xFFD700);

        return x + width;
    }



    /**
     * Gets the current time mode label for display.
     */
    private String getCurrentTimeModeLabel() {
        switch (currentTimeMode) {
            case HOUR: return "Hour";
            case DAY: return "Day";
            case MONTH: return "Month";
            case YEAR: return "Year";
            default: return "Day";
        }
    }



    /**
     * Draws an expandable button and returns the new X position.
     */
    private int drawExpandableButton(DrawContext context, int x, int y, String icon, String text, int color, float animation, boolean hovered) {
        int iconWidth = 24;
        int buttonHeight = 18;
        int fullWidth = 8 + this.textRenderer.getWidth(icon) + 6 + this.textRenderer.getWidth(text) + 8;

        // Interpolate width smoothly
        int currentWidth = (int)(iconWidth + (fullWidth - iconWidth) * animation);

        // Draw button background with glass effect
        int cardColor = hovered ? 0x70505050 : 0x50404040;
        context.fill(x, y, x + currentWidth, y + buttonHeight, cardColor);

        // Glass highlights
        context.fill(x + 1, y + 1, x + currentWidth - 1, y + 2, 0x30FFFFFF);
        context.fill(x + 1, y + 1, x + 2, y + buttonHeight - 1, 0x20FFFFFF);

        // Draw icon
        int iconY = y + (buttonHeight - 8) / 2;
        context.drawTextWithShadow(this.textRenderer, icon, x + 6, iconY, color);

        // Draw text with fade-in animation
        if (animation > 0.1f) {
            int textX = x + iconWidth + 2;
            float textAlpha = Math.max(0.0f, (animation - 0.3f) / 0.7f);
            int alpha = (int)(255 * textAlpha);
            int textColor = (alpha << 24) | 0x00FFFFFF;
            context.drawTextWithShadow(this.textRenderer, text, textX, iconY, textColor);
        }

        return x + currentWidth;
    }

    /**
     * Draws a scrollbar for scrollable content.
     */
    private void drawScrollbar(DrawContext context, int x, int y, int width, int height, int scrollOffset, int totalHeight, int visibleHeight) {
        if (totalHeight <= visibleHeight) return;

        // Draw scrollbar track
        context.fill(x, y, x + width, y + height, 0x40000000);

        // Calculate scrollbar thumb
        float scrollRatio = (float) scrollOffset / (totalHeight - visibleHeight);
        float thumbRatio = (float) visibleHeight / totalHeight;
        int thumbHeight = Math.max(10, (int)(height * thumbRatio));
        int thumbY = y + (int)((height - thumbHeight) * scrollRatio);

        // Draw scrollbar thumb
        context.fill(x + 1, thumbY, x + width - 1, thumbY + thumbHeight, 0x80FFFFFF);
    }

    /**
     * Plays the button click sound.
     */
    private void playClickSound() {
        SoundUtil.playButtonClickSound();
    }

    /**
     * Gets the display text for a town player rank.
     *
     * @param rank The rank
     * @return The display text
     */
    private String getRankText(TownPlayerRank rank) {
        switch (rank) {
            case OWNER:
                return "Mayor";
            case ADMIN:
                return "Deputy";
            case MODERATOR:
                return "Council";
            case MEMBER:
                return "Resident";
            case VISITOR:
                return "Citizen";
            default:
                return "Unknown";
        }
    }

    @Override
    public void resize(MinecraftClient client, int width, int height) {
        super.resize(client, width, height);

        // Recalculate panel dimensions
        panelWidth = Math.min(width - 20, 1100);
        panelHeight = height - 20;

        // Reinitialize subcategory positions
        initializeSubcategoryPositions();
    }

    @Override
    public void close() {
        // Unregister from chat viewing when closing screen
        if (isChatViewerRegistered && currentChatTownId != null) {
            TownChatClientManager.getInstance().unregisterViewer(currentChatTownId);
            isChatViewerRegistered = false;
            currentChatTownId = null;
        }

        // Clear the player list cache when closing the screen
        lastTownId = null;
        cachedPlayerList = null;
        this.client.setScreen(parent);
    }



    /**
     * Checks if a point is inside a circle.
     *
     * @param pointX The x coordinate of the point
     * @param pointY The y coordinate of the point
     * @param circleX The x coordinate of the circle center
     * @param circleY The y coordinate of the circle center
     * @param radius The radius of the circle
     * @return True if the point is inside the circle, false otherwise
     */
    private boolean isPointInCircle(int pointX, int pointY, int circleX, int circleY, int radius) {
        int dx = pointX - circleX;
        int dy = pointY - circleY;
        return dx * dx + dy * dy <= radius * radius;
    }

    /**
     * Draws the default town icon (house) in the center of the circle.
     *
     * @param context The draw context
     * @param centerX The x coordinate of the center
     * @param centerY The y coordinate of the center
     */
    private void drawDefaultTownIcon(DrawContext context, float centerX, float centerY) {
        String townIcon = "🏠"; // House icon
        int iconWidth = this.textRenderer.getWidth(townIcon);
        context.drawTextWithShadow(this.textRenderer, townIcon,
            (int)(centerX - iconWidth/2), (int)(centerY - 8), 0xFFFFFF);
    }













    /**
     * Draws a rounded rectangle effect (actual corners aren't rounded, but it creates that visual effect)
     */
    private void drawRoundedRect(DrawContext context, int x, int y, int width, int height, int color) {
        // Main rectangle
        context.fill(x, y, x + width, y + height, color);

        // Lighter top and left edges for rounded effect
        int lightEdge = (color & 0x00FFFFFF) | 0x10FFFFFF;
        context.fill(x, y, x + width, y + 1, lightEdge);
        context.fill(x, y, x + 1, y + height, lightEdge);

        // Darker bottom and right edges for rounded effect
        int darkEdge = (color & 0x00FFFFFF) | 0x10000000;
        context.fill(x, y + height - 1, x + width, y + height, darkEdge);
        context.fill(x + width - 1, y, x + width, y + height, darkEdge);
    }

    /**
     * Draws a circle using a series of small rectangles.
     *
     * @param context The draw context
     * @param centerX The x coordinate of the circle center
     * @param centerY The y coordinate of the circle center
     * @param radius The radius of the circle
     * @param color The color of the circle (ARGB format)
     * @param filled Whether to fill the circle or just draw the outline
     */
    private void drawCircle(DrawContext context, float centerX, float centerY, float radius, int color, boolean filled) {
        int x0 = (int)(centerX - radius);
        int y0 = (int)(centerY - radius);
        int x1 = (int)(centerX + radius);
        int y1 = (int)(centerY + radius);

        if (filled) {
            // Draw a filled circle using small rectangles
            for (int x = x0; x <= x1; x++) {
                for (int y = y0; y <= y1; y++) {
                    int dx = x - (int)centerX;
                    int dy = y - (int)centerY;
                    if (dx * dx + dy * dy <= radius * radius) {
                        context.fill(x, y, x + 1, y + 1, color);
                    }
                }
            }
        } else {
            // Draw just the outline
            for (int x = x0; x <= x1; x++) {
                for (int y = y0; y <= y1; y++) {
                    int dx = x - (int)centerX;
                    int dy = y - (int)centerY;
                    int distSq = dx * dx + dy * dy;
                    if (distSq <= radius * radius && distSq >= (radius - 1) * (radius - 1)) {
                        context.fill(x, y, x + 1, y + 1, color);
                    }
                }
            }
        }
    }

    /**
     * Draws a glass effect panel matching the subcategory styling
     */
    private void drawGlassPanel(DrawContext context, int x, int y, int width, int height) {
        // Main panel background - darker glass effect
        context.fill(x, y, x + width, y + height, GLASS_PANEL_BG);

        // Glass effect borders - matching subcategory style
        context.fill(x, y, x + width, y + 1, GLASS_BRIGHT_HIGHLIGHT); // Top highlight
        context.fill(x, y, x + 1, y + height, GLASS_TOP_HIGHLIGHT); // Left highlight
        context.fill(x + width - 1, y, x + width, y + height, GLASS_TOP_HIGHLIGHT); // Right highlight
        context.fill(x, y + height - 1, x + width, y + height, GLASS_SHADOW); // Bottom shadow

        // Inner glass effect for depth
        context.fill(x + 1, y + 1, x + width - 1, y + 2, GLASS_TOP_HIGHLIGHT);
        context.fill(x + 1, y + 1, x + 2, y + height - 1, 0x15FFFFFF);
    }

    /**
     * Draws the glass effect header section matching subcategory styling
     */
    private void drawGlassHeader(DrawContext context, int x, int y, int width, int height) {
        // Header background with glass effect - matching subcategory headers
        context.fill(x, y, x + width, y + height, GLASS_HEADER_BG);

        // Glass effect borders - exactly like subcategory headers
        context.fill(x, y, x + width, y + 1, GLASS_TOP_HIGHLIGHT); // Top highlight
        context.fill(x, y, x + 1, y + height, GLASS_TOP_HIGHLIGHT); // Left highlight

        // Inner glass effect for depth
        context.fill(x + 1, y + 1, x + width - 1, y + 2, GLASS_TOP_HIGHLIGHT);
        context.fill(x + 1, y + 1, x + 2, y + height - 1, 0x15FFFFFF);

        // Header title
        String title = "My Town";
        int titleWidth = this.textRenderer.getWidth(title);
        int titleX = x + (width - titleWidth) / 2;
        int titleY = y + (height - 8) / 2;

        // Subtle title glow effect
        context.fillGradient(
            titleX - 4, titleY - 1,
            titleX + titleWidth + 4, titleY + 10,
            0x20FFFFFF, 0x08FFFFFF
        );

        // Draw title text
        context.drawTextWithShadow(this.textRenderer, title, titleX, titleY, TEXT_PRIMARY);
    }

    /**
     * Draws a glass effect card matching the subcategory styling
     */
    private void drawGlassCard(DrawContext context, int x, int y, int width, int height, int bgColor, boolean elevated) {
        // Draw card background
        context.fill(x, y, x + width, y + height, bgColor);

        // Glass effect borders
        context.fill(x, y, x + width, y + 1, GLASS_TOP_HIGHLIGHT); // Top highlight
        context.fill(x, y, x + 1, y + height, GLASS_TOP_HIGHLIGHT); // Left highlight

        if (elevated) {
            // Enhanced glass effect for elevated cards
            context.fill(x + width - 1, y, x + width, y + height, GLASS_BOTTOM_SHADOW); // Right shadow
            context.fill(x, y + height - 1, x + width, y + height, GLASS_BOTTOM_SHADOW); // Bottom shadow
        }

        // Add subtle inner highlight
        context.fill(x + 1, y + 1, x + width - 1, y + 2, 0x10FFFFFF);
    }

    /**
     * Blends two colors together
     */
    private int blendColors(int color1, int color2, float ratio) {
        int a1 = (color1 >> 24) & 0xFF;
        int r1 = (color1 >> 16) & 0xFF;
        int g1 = (color1 >> 8) & 0xFF;
        int b1 = color1 & 0xFF;

        int a2 = (color2 >> 24) & 0xFF;
        int r2 = (color2 >> 16) & 0xFF;
        int g2 = (color2 >> 8) & 0xFF;
        int b2 = color2 & 0xFF;

        int a = (int) (a1 + (a2 - a1) * ratio);
        int r = (int) (r1 + (r2 - r1) * ratio);
        int g = (int) (g1 + (g2 - g1) * ratio);
        int b = (int) (b1 + (b2 - b1) * ratio);

        return (a << 24) | (r << 16) | (g << 8) | b;
    }

    @Override
    public void removed() {
        super.removed();

        // Unregister from town player updates when the screen is closed
        TownDataSynchronizer.unregisterPlayerUpdateCallback(this);

        // Unregister from election updates
        ElectionNetworkHandler.unregisterElectionUpdateCallback(this);

        // Unregister from client sync manager events
        com.pokecobble.town.client.ClientSyncManager.getInstance().removeEventListener("player_updated", this::onPlayerDataUpdated);
        com.pokecobble.town.client.ClientSyncManager.getInstance().removeEventListener("town_list_updated", this::onTownListUpdated);
        com.pokecobble.town.client.ClientSyncManager.getInstance().removeEventListener("town_updated", this::onTownDataUpdated);
        com.pokecobble.town.client.ClientSyncManager.getInstance().removeEventListener("chunk_updated", this::onChunkDataUpdated);

        // Unregister from UIDataRefreshManager
        com.pokecobble.ui.UIDataRefreshManager.getInstance().onScreenClosed(this);
    }



    /**
     * Clears the image settings tracking for a specific town.
     * This should be called when the server sends an image update to allow reloading.
     */


    /**
     * Clears all image settings tracking.
     * This should be called when the screen is refreshed or when major data changes occur.
     */




    /**
     * Safely requests town data with cooldown to prevent spam.
     */
    private void safeRequestTownData() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastTownDataRequestTime >= REQUEST_COOLDOWN_MS) {
            lastTownDataRequestTime = currentTime;
            TownNetworkHandler.requestTownData();
        }
    }

    /**
     * Safely requests config sync with cooldown to prevent spam.
     */
    private void safeRequestConfigSync() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastConfigRequestTime >= REQUEST_COOLDOWN_MS) {
            lastConfigRequestTime = currentTime;
            com.pokecobble.config.ConfigSynchronizer.requestPlayerTownSettings();
        }
    }

    /**
     * Shows a permission denied notification at the bottom of the screen.
     *
     * @param featureName The name of the feature that was denied
     */
    public void showPermissionNotification(String featureName) {
        this.permissionNotificationText = "You don't have permission to access " + featureName;
        this.permissionNotificationColor = 0xFFFF5555; // Red color
        this.permissionNotificationStartTime = System.currentTimeMillis();
    }

    /**
     * Shows the comprehensive ban screen for a player.
     * Creates a detailed ban dialog with reason input, duration selection, and proper validation.
     */
    private void showBanConfirmationScreen(TownPlayer player) {
        BanPlayerScreen banScreen = new BanPlayerScreen(
            this,
            player,
            (reason, isPermanent, durationHours) -> {
                // Confirm action - send ban request to server with details
                com.pokecobble.town.network.town.TownNetworkHandler.requestBanPlayerWithDetails(
                    player.getUuid(), reason, isPermanent, durationHours);
            },
            () -> {
                // Cancel action - do nothing, just close the dialog
            }
        );

        if (client != null) {
            client.setScreen(banScreen);
        }
    }

    /**
     * Shows the kick confirmation screen for a player.
     * Creates a confirmation dialog that matches MyTownScreen design with proper screen resize support.
     */
    private void showKickConfirmationScreen(TownPlayer player) {
        String title = "Kick Player";
        String message = "Are you sure you want to kick " + player.getName() + " from the town?";

        PlayerActionConfirmationScreen confirmationScreen = new PlayerActionConfirmationScreen(
            this, title, message, player,
            () -> {
                // Confirm action - send kick request to server
                com.pokecobble.town.network.town.TownNetworkHandler.requestKickPlayer(player.getUuid());
            },
            () -> {
                // Cancel action - do nothing, just close the dialog
            }
        );

        if (client != null) {
            client.setScreen(confirmationScreen);
        }
    }

    /**
     * Updates the permission notification state and removes expired notifications.
     */
    private void updatePermissionNotification() {
        if (permissionNotificationText != null) {
            long currentTime = System.currentTimeMillis();
            long elapsedTime = currentTime - permissionNotificationStartTime;

            // Remove notification after duration (convert ticks to milliseconds: 100 ticks = 5000ms)
            if (elapsedTime >= (PERMISSION_NOTIFICATION_DURATION * 50)) {
                permissionNotificationText = null;
            }
        }
    }

    /**
     * Renders the permission notification at the bottom of the screen.
     */
    private void renderPermissionNotification(DrawContext context) {
        if (permissionNotificationText == null) {
            return;
        }

        // Calculate notification position (bottom center of the panel)
        int leftX = (width - panelWidth) / 2;
        int topY = 10;
        int notificationY = topY + panelHeight - 30; // 30 pixels from bottom of panel

        // Calculate fade effect based on time
        long currentTime = System.currentTimeMillis();
        long elapsedTime = currentTime - permissionNotificationStartTime;
        float alpha = 1.0f;

        // Fade in during first 500ms
        if (elapsedTime < 500) {
            alpha = elapsedTime / 500.0f;
        }
        // Fade out during last 1000ms
        else if (elapsedTime > (PERMISSION_NOTIFICATION_DURATION * 50) - 1000) {
            long fadeTime = elapsedTime - ((PERMISSION_NOTIFICATION_DURATION * 50) - 1000);
            alpha = 1.0f - (fadeTime / 1000.0f);
        }

        // Ensure alpha is within bounds
        alpha = Math.max(0.0f, Math.min(1.0f, alpha));

        // Calculate text width for background
        int textWidth = this.textRenderer.getWidth(permissionNotificationText);
        int backgroundWidth = textWidth + 20; // 10 pixels padding on each side
        int backgroundHeight = 20;
        int backgroundX = (width - backgroundWidth) / 2;
        int backgroundY = notificationY - 5;

        // Apply alpha to colors
        int backgroundAlpha = (int)(alpha * 0.8f * 255) << 24; // 80% opacity background
        int textAlpha = (int)(alpha * 255) << 24;

        // Draw background with glass effect matching MyTownScreen style
        int backgroundColor = backgroundAlpha | 0x000000; // Black background with alpha
        int borderColor = backgroundAlpha | 0x333333; // Dark gray border with alpha

        context.fill(backgroundX, backgroundY, backgroundX + backgroundWidth, backgroundY + backgroundHeight, backgroundColor);
        context.fill(backgroundX, backgroundY, backgroundX + backgroundWidth, backgroundY + 1, borderColor); // Top border
        context.fill(backgroundX, backgroundY + backgroundHeight - 1, backgroundX + backgroundWidth, backgroundY + backgroundHeight, borderColor); // Bottom border
        context.fill(backgroundX, backgroundY, backgroundX + 1, backgroundY + backgroundHeight, borderColor); // Left border
        context.fill(backgroundX + backgroundWidth - 1, backgroundY, backgroundX + backgroundWidth, backgroundY + backgroundHeight, borderColor); // Right border

        // Draw text with alpha
        int finalTextColor = textAlpha | (permissionNotificationColor & 0x00FFFFFF);
        context.drawCenteredTextWithShadow(this.textRenderer, permissionNotificationText, width / 2, notificationY, finalTextColor);
    }

    /**
     * Checks permission with retry logic to handle data synchronization delays.
     * This method will retry permission checks if they fail due to missing player data.
     */
    private boolean checkPermissionWithRetry(String permissionCategory, String permissionName, String featureName) {
        Town playerTown = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTown();
        if (playerTown == null) {
            return false;
        }

        // Check if player data exists in the town
        TownPlayer townPlayer = playerTown.getPlayer(client.player.getUuid());
        if (townPlayer == null && permissionRetryCount < MAX_PERMISSION_RETRIES) {
            long currentTime = System.currentTimeMillis();

            // Check if enough time has passed since last retry
            if (currentTime - lastPermissionRetryTime >= PERMISSION_RETRY_DELAY_MS) {
                permissionRetryCount++;
                lastPermissionRetryTime = currentTime;

                // With the new simplified permission system, permissions are automatically synced
                // No need to manually request sync

                // Show a temporary message that we're syncing data
                showPermissionNotification("Syncing player data...");
                return false; // Don't proceed with action yet
            } else {
                // Still within retry delay, show waiting message
                showPermissionNotification("Checking permissions...");
                return false;
            }
        }

        // Player data is available, check permission normally
        boolean hasPermission = PermissionChecker.checkPermissionOrShowNotification(this, playerTown, client.player.getUuid(),
                permissionCategory, permissionName, featureName);

        // Reset retry count on successful permission check or when player data is available
        if (hasPermission || townPlayer != null) {
            permissionRetryCount = 0;
        }

        return hasPermission;
    }

    /**
     * Initializes chat components for the town chat system.
     */
    private void initializeChatComponents(int x, int y, int width, int height, Town playerTown) {
        if (playerTown == null) return;

        // Calculate layout dimensions - smaller input box for more compact design
        int inputBoxHeight = 24; // Reduced from 30 to 24 for smaller vertical size
        int displayAreaHeight = height - inputBoxHeight - 8; // Reduced spacing from 10 to 8
        int margin = 5;

        // Initialize chat display area if needed
        if (chatDisplayArea == null) {
            chatDisplayArea = new ChatDisplayArea(textRenderer,
                x + margin, y + margin,
                width - margin * 2, displayAreaHeight - margin);
        } else {
            chatDisplayArea.updatePosition(x + margin, y + margin,
                width - margin * 2, displayAreaHeight - margin);
        }

        // Initialize chat input box if needed
        if (chatInputBox == null) {
            chatInputBox = new AnimatedChatInputBox(textRenderer,
                x + margin, y + displayAreaHeight + 5,
                width - margin * 2, "Type a message...", 256);

            // Set up submit listener
            chatInputBox.setSubmitListener(message -> {
                if (message != null && !message.trim().isEmpty()) {
                    TownChatClientManager.getInstance().sendMessage(playerTown.getId(), message);
                }
            });
        } else {
            chatInputBox.updatePosition(x + margin, y + displayAreaHeight + 5, width - margin * 2);
        }

        // Register as chat viewer if not already registered or town changed
        if (!isChatViewerRegistered || !playerTown.getId().equals(currentChatTownId)) {
            if (isChatViewerRegistered && currentChatTownId != null) {
                // Unregister from previous town
                TownChatClientManager.getInstance().unregisterViewer(currentChatTownId);
            }

            // Register for current town
            TownChatClientManager.getInstance().registerViewer(playerTown.getId());

            // Only request history if we don't have any messages cached
            // This prevents clearing existing messages when reopening the screen
            int existingMessageCount = TownChatClientManager.getInstance().getMessageCount(playerTown.getId());
            if (existingMessageCount == 0) {
                Pokecobbleclaim.LOGGER.debug("No cached messages for town {}, requesting history from server", playerTown.getId());
                TownChatClientManager.getInstance().requestHistory(playerTown.getId());
            } else {
                Pokecobbleclaim.LOGGER.debug("Found {} cached messages for town {}, skipping history request", existingMessageCount, playerTown.getId());
            }

            isChatViewerRegistered = true;
            currentChatTownId = playerTown.getId();
            lastMessageCount = TownChatClientManager.getInstance().getMessageCount(playerTown.getId());
        }
    }



}
