package com.pokecobble.town.gui;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.network.town.InviteNetworkHandler;
import com.pokecobble.town.sound.SoundUtil;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.network.PlayerListEntry;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;
import net.minecraft.util.Identifier;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * Screen for inviting players to a town with modern glass effect styling matching MyTownScreen.
 */
@Environment(EnvType.CLIENT)
public class InviteScreen extends Screen {
    private final Screen parent;
    private final Town town;

    // Glass Effect Color Palette - matching MyTownScreen styling
    private static final int GLASS_PANEL_BG = 0xD0101010;      // Main panel background
    private static final int GLASS_HEADER_BG = 0x60404040;     // Header glass background
    private static final int GLASS_CONTENT_BG = 0x30000000;    // Content area background
    private static final int GLASS_CARD_BG = 0x40303030;       // Card backgrounds
    private static final int GLASS_CARD_HOVER = 0x60404040;    // Card hover state

    // Glass effect highlights and shadows
    private static final int GLASS_TOP_HIGHLIGHT = 0x20FFFFFF;    // Top glass highlight
    private static final int GLASS_BRIGHT_HIGHLIGHT = 0x40FFFFFF; // Brighter highlights
    private static final int GLASS_INNER_HIGHLIGHT = 0x30FFFFFF;  // Inner glass highlights
    private static final int GLASS_SHADOW = 0x40000000;          // Glass shadows
    private static final int GLASS_BOTTOM_SHADOW = 0x20000000;   // Bottom shadows

    // Text colors
    private static final int TEXT_PRIMARY = 0xFFFFFFFF;     // Primary text
    private static final int TEXT_SECONDARY = 0xFFB0B0B0;   // Secondary text
    private static final int TEXT_MUTED = 0xFF808080;       // Muted text

    // Button colors matching MyTownScreen
    private static final int BUTTON_INVITE = 0xFF4CAF50;    // Green for invite
    private static final int BUTTON_BACK = 0xFFE53935;      // Red for back
    private static final int BUTTON_REFRESH = 0xFF2196F3;   // Blue for refresh

    // Panel dimensions - matching ModernTownScreen sizing
    private int panelWidth = 600;
    private int panelHeight = 400;
    private int leftX;
    private int topY;

    // Improved spacing system matching ModernTownScreen
    private static final int SPACING_XS = 4;
    private static final int SPACING_SM = 8;
    private static final int SPACING_MD = 12;
    private static final int SPACING_LG = 16;
    private static final int SPACING_XL = 24;

    // Scrolling
    private int scrollOffset = 0;
    private static final int SCROLL_AMOUNT = 20;       // Matching ModernTownScreen

    // Status message
    private Text statusText = Text.empty();
    private int statusColor = TEXT_PRIMARY;
    private int statusTimer = 0;

    // Player list
    private List<PlayerEntry> playerEntries = new ArrayList<>();

    // Constants for layout - optimized for more compact display
    private static final int ENTRY_HEIGHT = 22;        // Reduced for more entries
    private static final int ENTRY_SPACING = 1;        // Reduced spacing
    private static final int BUTTON_WIDTH = 80;        // Consistent button width
    private static final int BUTTON_HEIGHT = 20;       // Reduced button height
    private static final int HEADER_HEIGHT = 20;       // Reduced header height

    // Network constants
    private static final Identifier INVITE_PACKET_ID = InviteNetworkHandler.INVITE_RESPONSE;

    /**
     * Creates a new invite screen.
     *
     * @param parent The parent screen
     * @param town The town to invite players to
     */
    public InviteScreen(Screen parent, Town town) {
        super(Text.literal("Invite Players"));
        this.parent = parent;
        this.town = town;

        // Check permission before allowing access
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.player != null) {
            if (!PermissionChecker.checkPermissionOrShowDenied(parent, town, client.player.getUuid(),
                    PermissionChecker.Permissions.PLAYER_MANAGEMENT,
                    PermissionChecker.Permissions.CAN_INVITE_PLAYERS,
                    PermissionChecker.FeatureNames.PLAYER_INVITATIONS)) {
                return; // Permission denied screen was shown
            }
        }
    }

    @Override
    protected void init() {
        super.init();

        // Calculate dynamic panel dimensions matching ModernTownScreen style
        panelWidth = Math.min(width - 20, 800); // Responsive width with max limit like ModernTownScreen
        panelHeight = height - 20; // Full height minus margins like ModernTownScreen
        leftX = (width - panelWidth) / 2;
        topY = 10; // Top positioning like ModernTownScreen

        // Load player list
        loadPlayerList();

        // Register packet handler for invite response
        registerPacketHandler();
    }

    /**
     * Loads the list of players that can be invited.
     */
    private void loadPlayerList() {
        playerEntries.clear();

        // Get all online players from the player list
        if (client != null && client.getNetworkHandler() != null) {
            for (PlayerListEntry entry : client.getNetworkHandler().getPlayerList()) {
                UUID playerId = entry.getProfile().getId();
                String playerName = entry.getProfile().getName();

                // Skip the current player
                if (client.player != null && playerId.equals(client.player.getUuid())) {
                    continue;
                }

                // Check if player is already in a town using proper client-side checking
                if (isPlayerInAnyTown(playerId)) {
                    continue;
                }

                playerEntries.add(new PlayerEntry(playerId, playerName));
            }
        }

        // Sort the list alphabetically
        playerEntries.sort((a, b) -> a.name.compareToIgnoreCase(b.name));
    }

    /**
     * Checks if a player is in any town using client-side data.
     * This method properly checks all cached towns to determine if a player is a member.
     */
    private boolean isPlayerInAnyTown(UUID playerId) {
        try {
            // Get all cached towns from ClientTownManager
            List<Town> allTowns = com.pokecobble.town.client.ClientTownManager.getInstance().getAllTowns();

            // Check each town to see if the player is a member
            for (Town town : allTowns) {
                if (town.getPlayer(playerId) != null) {
                    return true;
                }
            }

            return false;
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.warn("Error checking if player {} is in a town: {}", playerId, e.getMessage());
            return false; // If we can't check, assume they're not in a town to be safe
        }
    }

    /**
     * Registers the packet handler for invite responses.
     */
    private void registerPacketHandler() {
        // The packet handler is registered in InviteNetworkHandler
        // This method is kept for future extensions
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Render dark background
        this.renderBackground(context);

        // Draw glass effect panel matching MyTownScreen style
        drawGlassPanel(context, leftX, topY, panelWidth, panelHeight);

        // Draw glass effect header
        drawGlassHeader(context, leftX, topY, panelWidth, HEADER_HEIGHT);

        // Draw title with compact styling
        String titleText = "Invite Players to " + town.getName();
        int titleWidth = this.textRenderer.getWidth(titleText);
        int titleX = leftX + (panelWidth - titleWidth) / 2;  // Center the title
        int titleY = topY + SPACING_XS; // Using compact spacing
        context.drawTextWithShadow(this.textRenderer, Text.literal(titleText).formatted(Formatting.BOLD),
                titleX, titleY, TEXT_PRIMARY);

        // Calculate content area with optimized spacing
        int contentX = leftX + SPACING_MD;
        int contentY = topY + HEADER_HEIGHT + SPACING_XS;
        int contentWidth = panelWidth - (SPACING_MD * 2);
        int contentHeight = panelHeight - HEADER_HEIGHT - 40; // Reduced space for buttons at bottom

        // Draw glass effect content area background
        drawGlassContentArea(context, contentX, contentY, contentWidth, contentHeight);

        // Draw player list header with compact styling
        int headerY = contentY + SPACING_XS;
        context.drawTextWithShadow(this.textRenderer, Text.literal("Available Players").formatted(Formatting.BOLD),
                contentX + SPACING_MD, headerY, TEXT_PRIMARY);

        // Draw subtitle with reduced spacing
        String subtitleText = "Players not currently in a town";
        context.drawTextWithShadow(this.textRenderer, Text.literal(subtitleText),
                contentX + SPACING_MD, headerY + SPACING_SM, TEXT_SECONDARY);

        // Draw modern divider with glass effect - reduced spacing
        int dividerY = headerY + SPACING_MD + SPACING_XS; // 12 + 4 = 16px spacing
        context.fill(contentX + SPACING_SM, dividerY, contentX + contentWidth - SPACING_SM, dividerY + 1, GLASS_TOP_HIGHLIGHT);

        // Calculate visible area for player entries - maximized area
        int entriesAreaY = dividerY + SPACING_XS;
        int entriesAreaHeight = contentHeight - (SPACING_LG + SPACING_MD); // Reduced spacing calculation

        // Calculate total height of all entries
        int totalHeight = playerEntries.size() * (ENTRY_HEIGHT + ENTRY_SPACING);
        if (totalHeight > 0) {
            totalHeight += 10; // Add padding at bottom
        }

        // Calculate max scroll
        int maxScroll = Math.max(0, totalHeight - entriesAreaHeight);
        scrollOffset = Math.min(scrollOffset, maxScroll);

        // Draw modern scrollbar if needed
        if (maxScroll > 0) {
            drawModernScrollbar(context, contentX + contentWidth - SPACING_MD, entriesAreaY,
                              SPACING_SM, entriesAreaHeight, scrollOffset, maxScroll);
        }

        // Apply scissor to clip content to visible area
        context.enableScissor(
            contentX + SPACING_SM,
            entriesAreaY,
            contentX + contentWidth - SPACING_LG,  // Account for scrollbar space
            entriesAreaY + entriesAreaHeight
        );

        // Draw player entries with modern glass card styling
        int entryY = entriesAreaY - scrollOffset;

        if (playerEntries.isEmpty()) {
            // Show empty state message
            String emptyMessage = "No players available to invite";
            int messageWidth = this.textRenderer.getWidth(emptyMessage);
            int messageX = contentX + (contentWidth - messageWidth) / 2;
            int messageY = entriesAreaY + entriesAreaHeight / 2;
            context.drawTextWithShadow(this.textRenderer, Text.literal(emptyMessage),
                    messageX, messageY, TEXT_MUTED);
        } else {
            for (PlayerEntry entry : playerEntries) {
                // Skip if entry is completely outside visible area
                if (entryY + ENTRY_HEIGHT < entriesAreaY || entryY > entriesAreaY + entriesAreaHeight) {
                    entryY += ENTRY_HEIGHT + ENTRY_SPACING;
                    continue;
                }

                // Check if entry is hovered
                boolean isHovered = mouseX >= contentX + SPACING_SM && mouseX <= contentX + contentWidth - SPACING_LG &&
                                   mouseY >= entryY && mouseY <= entryY + ENTRY_HEIGHT &&
                                   mouseY >= entriesAreaY && mouseY <= entriesAreaY + entriesAreaHeight;

                // Draw modern glass card background
                int cardBg = isHovered ? GLASS_CARD_HOVER : GLASS_CARD_BG;
                drawGlassCard(context, contentX + SPACING_SM, entryY, contentWidth - SPACING_XL - SPACING_SM, ENTRY_HEIGHT, cardBg);

                // Draw player name with ModernTownScreen positioning
                context.drawTextWithShadow(this.textRenderer, entry.name,
                        contentX + SPACING_LG, entryY + (ENTRY_HEIGHT - 8) / 2, TEXT_PRIMARY);

                // Draw modern invite button
                int buttonX = contentX + contentWidth - BUTTON_WIDTH - SPACING_XL;
                int buttonY = entryY + (ENTRY_HEIGHT - BUTTON_HEIGHT) / 2;
                boolean buttonHovered = mouseX >= buttonX && mouseX <= buttonX + BUTTON_WIDTH &&
                                       mouseY >= buttonY && mouseY <= buttonY + BUTTON_HEIGHT &&
                                       mouseY >= entriesAreaY && mouseY <= entriesAreaY + entriesAreaHeight;

                drawGlassButton(context, buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT,
                        BUTTON_INVITE, buttonHovered, "Invite");

                entryY += ENTRY_HEIGHT + ENTRY_SPACING;
            }
        }

        // Disable scissor
        context.disableScissor();

        // Draw bottom button bar with glass effect using compact spacing
        int buttonBarY = topY + panelHeight - (SPACING_SM + BUTTON_HEIGHT + SPACING_XS);
        int buttonBarHeight = SPACING_SM + BUTTON_HEIGHT + SPACING_XS;
        drawGlassHeader(context, leftX, buttonBarY, panelWidth, buttonBarHeight);

        // Draw back button with ModernTownScreen styling
        int backButtonX = leftX + SPACING_MD;
        int backButtonY = buttonBarY + (buttonBarHeight - BUTTON_HEIGHT) / 2;
        boolean backButtonHovered = mouseX >= backButtonX && mouseX <= backButtonX + BUTTON_WIDTH &&
                                   mouseY >= backButtonY && mouseY <= backButtonY + BUTTON_HEIGHT;

        drawGlassButton(context, backButtonX, backButtonY, BUTTON_WIDTH, BUTTON_HEIGHT,
                BUTTON_BACK, backButtonHovered, "Back");

        // Draw refresh button with ModernTownScreen styling
        int refreshButtonX = leftX + panelWidth - BUTTON_WIDTH - SPACING_MD;
        int refreshButtonY = backButtonY;
        boolean refreshButtonHovered = mouseX >= refreshButtonX && mouseX <= refreshButtonX + BUTTON_WIDTH &&
                                      mouseY >= refreshButtonY && mouseY <= refreshButtonY + BUTTON_HEIGHT;

        drawGlassButton(context, refreshButtonX, refreshButtonY, BUTTON_WIDTH, BUTTON_HEIGHT,
                BUTTON_REFRESH, refreshButtonHovered, "Refresh");

        // Draw status message if present with ModernTownScreen styling
        if (statusTimer > 0) {
            // Draw status background
            int statusBgY = buttonBarY - SPACING_XL - SPACING_XS;
            context.fill(leftX + SPACING_LG, statusBgY, leftX + panelWidth - SPACING_LG, statusBgY + SPACING_LG + SPACING_XS, GLASS_CARD_BG);
            context.fill(leftX + SPACING_LG, statusBgY, leftX + panelWidth - SPACING_LG, statusBgY + 1, GLASS_TOP_HIGHLIGHT);

            context.drawCenteredTextWithShadow(this.textRenderer, statusText,
                    leftX + panelWidth / 2, statusBgY + SPACING_SM - 2, statusColor);
            statusTimer--;
        }

        super.render(context, mouseX, mouseY, delta);
    }

    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        if (button == 0) { // Left click
            // Calculate content area with compact spacing
            int contentX = leftX + SPACING_MD;
            int contentY = topY + HEADER_HEIGHT + SPACING_XS;
            int contentWidth = panelWidth - (SPACING_MD * 2);
            int contentHeight = panelHeight - HEADER_HEIGHT - 40;

            // Calculate visible area for player entries
            int headerY = contentY + SPACING_XS;
            int dividerY = headerY + SPACING_MD + SPACING_XS;
            int entriesAreaY = dividerY + SPACING_XS;
            int entriesAreaHeight = contentHeight - (SPACING_LG + SPACING_MD);

            // Check for clicks on modern scrollbar
            if (mouseX >= contentX + contentWidth - SPACING_MD && mouseX <= contentX + contentWidth - SPACING_XS &&
                mouseY >= entriesAreaY && mouseY <= entriesAreaY + entriesAreaHeight) {

                // Calculate total height of all entries
                int totalHeight = playerEntries.size() * (ENTRY_HEIGHT + ENTRY_SPACING);
                if (totalHeight > 0) {
                    totalHeight += 10; // Add padding at bottom
                }

                // Calculate max scroll
                int maxScroll = Math.max(0, totalHeight - entriesAreaHeight);

                if (maxScroll > 0) {
                    // Calculate new scroll position based on click position
                    float clickPosition = (float)(mouseY - entriesAreaY) / entriesAreaHeight;
                    scrollOffset = Math.round(clickPosition * maxScroll);
                    scrollOffset = Math.max(0, Math.min(scrollOffset, maxScroll));
                    playClickSound();
                    return true;
                }
            }

            // Check for clicks on player entries
            if (mouseX >= contentX + SPACING_SM && mouseX <= contentX + contentWidth - SPACING_LG &&
                mouseY >= entriesAreaY && mouseY <= entriesAreaY + entriesAreaHeight) {

                int entryY = entriesAreaY - scrollOffset;

                for (PlayerEntry entry : playerEntries) {
                    // Skip if entry is completely outside visible area
                    if (entryY + ENTRY_HEIGHT < entriesAreaY || entryY > entriesAreaY + entriesAreaHeight) {
                        entryY += ENTRY_HEIGHT + ENTRY_SPACING;
                        continue;
                    }

                    // Check if click is on the invite button
                    int buttonX = contentX + contentWidth - BUTTON_WIDTH - SPACING_XL;
                    int buttonY = entryY + (ENTRY_HEIGHT - BUTTON_HEIGHT) / 2;
                    if (mouseX >= buttonX && mouseX <= buttonX + BUTTON_WIDTH &&
                        mouseY >= buttonY && mouseY <= buttonY + BUTTON_HEIGHT) {

                        // Invite the player
                        invitePlayer(entry);
                        playClickSound();
                        return true;
                    }

                    entryY += ENTRY_HEIGHT + ENTRY_SPACING;
                }
            }

            // Check back button
            int buttonBarY = topY + panelHeight - (SPACING_LG + BUTTON_HEIGHT + SPACING_SM);
            int buttonBarHeight = SPACING_LG + BUTTON_HEIGHT + SPACING_SM;
            int backButtonX = leftX + SPACING_MD;
            int backButtonY = buttonBarY + (buttonBarHeight - BUTTON_HEIGHT) / 2;
            if (mouseX >= backButtonX && mouseX <= backButtonX + BUTTON_WIDTH &&
                mouseY >= backButtonY && mouseY <= backButtonY + BUTTON_HEIGHT) {

                // Return to parent screen
                this.client.setScreen(parent);
                playClickSound();
                return true;
            }

            // Check refresh button
            int refreshButtonX = leftX + panelWidth - BUTTON_WIDTH - SPACING_MD;
            int refreshButtonY = backButtonY;
            if (mouseX >= refreshButtonX && mouseX <= refreshButtonX + BUTTON_WIDTH &&
                mouseY >= refreshButtonY && mouseY <= refreshButtonY + BUTTON_HEIGHT) {

                // Refresh player list
                loadPlayerList();
                setStatus("Player list refreshed", BUTTON_REFRESH);
                playClickSound();
                return true;
            }
        }

        return super.mouseClicked(mouseX, mouseY, button);
    }

    @Override
    public boolean mouseScrolled(double mouseX, double mouseY, double amount) {
        // Calculate content area with compact spacing
        int contentX = leftX + SPACING_MD;
        int contentY = topY + HEADER_HEIGHT + SPACING_XS;
        int contentWidth = panelWidth - (SPACING_MD * 2);
        int contentHeight = panelHeight - HEADER_HEIGHT - 40;

        // Calculate visible area for player entries
        int headerY = contentY + SPACING_XS;
        int dividerY = headerY + SPACING_MD + SPACING_XS;
        int entriesAreaY = dividerY + SPACING_XS;
        int entriesAreaHeight = contentHeight - (SPACING_LG + SPACING_MD);

        // Check if mouse is in the entries area
        if (mouseX >= contentX + SPACING_SM && mouseX <= contentX + contentWidth - SPACING_LG &&
            mouseY >= entriesAreaY && mouseY <= entriesAreaY + entriesAreaHeight) {

            // Calculate total height of all entries
            int totalHeight = playerEntries.size() * (ENTRY_HEIGHT + ENTRY_SPACING);
            if (totalHeight > 0) {
                totalHeight += 10; // Add padding at bottom
            }

            // Calculate max scroll
            int maxScroll = Math.max(0, totalHeight - entriesAreaHeight);

            // Update scroll offset with smoother scrolling
            scrollOffset -= (int) (amount * SCROLL_AMOUNT);

            // Ensure scroll offset stays within bounds
            scrollOffset = Math.max(0, Math.min(scrollOffset, maxScroll));

            return true;
        }

        return super.mouseScrolled(mouseX, mouseY, amount);
    }

    /**
     * Invites a player to the town.
     *
     * @param player The player to invite
     */
    private void invitePlayer(PlayerEntry player) {
        try {
            // Send invitation using the network handler
            InviteNetworkHandler.invitePlayer(town.getId(), player.id);

            // Show status message
            setStatus("Invitation sent to " + player.name, 0x55FF55);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error inviting player: " + e.getMessage());
            setStatus("Error inviting player", 0xFF5555);
        }
    }

    /**
     * Sets a status message to display.
     *
     * @param message The message to display
     * @param color The color of the message
     */
    private void setStatus(String message, int color) {
        this.statusText = Text.literal(message);
        this.statusColor = color;
        this.statusTimer = 60; // Show for 3 seconds (60 ticks)
    }

    /**
     * Plays a click sound.
     */
    private void playClickSound() {
        SoundUtil.playButtonClickSound();
    }

    /**
     * Draws a glass effect panel matching MyTownScreen styling
     */
    private void drawGlassPanel(DrawContext context, int x, int y, int width, int height) {
        // Main panel background - darker glass effect
        context.fill(x, y, x + width, y + height, GLASS_PANEL_BG);

        // Glass effect borders - matching MyTownScreen style
        context.fill(x, y, x + width, y + 1, GLASS_BRIGHT_HIGHLIGHT); // Top highlight
        context.fill(x, y, x + 1, y + height, GLASS_TOP_HIGHLIGHT); // Left highlight
        context.fill(x + width - 1, y, x + width, y + height, GLASS_TOP_HIGHLIGHT); // Right highlight
        context.fill(x, y + height - 1, x + width, y + height, GLASS_SHADOW); // Bottom shadow
    }

    /**
     * Draws the glass effect header section matching ModernTownScreen styling
     */
    private void drawGlassHeader(DrawContext context, int x, int y, int width, int height) {
        // Header background with glass effect - matching ModernTownScreen headers
        context.fill(x, y, x + width, y + height, GLASS_HEADER_BG);

        // Glass effect borders - exactly like ModernTownScreen headers
        context.fill(x, y, x + width, y + 1, GLASS_TOP_HIGHLIGHT); // Top highlight
        context.fill(x, y, x + 1, y + height, GLASS_TOP_HIGHLIGHT); // Left highlight
        context.fill(x + width - 1, y, x + width, y + height, GLASS_TOP_HIGHLIGHT); // Right highlight

        // Inner glass effect for depth matching ModernTownScreen
        context.fill(x + 1, y + 1, x + width - 1, y + 2, GLASS_TOP_HIGHLIGHT);
        context.fill(x + 1, y + 1, x + 2, y + height - 1, 0x15FFFFFF);
    }

    /**
     * Draws the glass effect content area background
     */
    private void drawGlassContentArea(DrawContext context, int x, int y, int width, int height) {
        // Content area background with glass effect
        context.fill(x, y, x + width, y + height, GLASS_CONTENT_BG);

        // Glass effect borders
        context.fill(x, y, x + width, y + 1, GLASS_BRIGHT_HIGHLIGHT); // Top border
        context.fill(x, y, x + 1, y + height, GLASS_BRIGHT_HIGHLIGHT); // Left border
        context.fill(x + width - 1, y, x + width, y + height, GLASS_BOTTOM_SHADOW); // Right border
        context.fill(x, y + height - 1, x + width, y + height, GLASS_BOTTOM_SHADOW); // Bottom border
    }

    /**
     * Draws a glass effect card matching MyTownScreen styling
     */
    private void drawGlassCard(DrawContext context, int x, int y, int width, int height, int bgColor) {
        // Draw card background
        context.fill(x, y, x + width, y + height, bgColor);

        // Glass effect borders
        context.fill(x, y, x + width, y + 1, GLASS_TOP_HIGHLIGHT); // Top highlight
        context.fill(x, y, x + 1, y + height, GLASS_TOP_HIGHLIGHT); // Left highlight
        context.fill(x + width - 1, y, x + width, y + height, GLASS_BOTTOM_SHADOW); // Right shadow
        context.fill(x, y + height - 1, x + width, y + height, GLASS_BOTTOM_SHADOW); // Bottom shadow
    }

    /**
     * Draws a modern glass button matching MyTownScreen styling
     */
    private void drawGlassButton(DrawContext context, int x, int y, int width, int height, int color, boolean isHovered, String text) {
        // Extract RGB components from the accent color
        int red = (color >> 16) & 0xFF;
        int green = (color >> 8) & 0xFF;
        int blue = color & 0xFF;

        // Create glass effect background similar to MyTownScreen buttons
        int baseGlassAlpha = isHovered ? 0x40 : 0x25; // Lower opacity for glass effect
        int baseGlassColor = (baseGlassAlpha << 24) | 0x303030; // Dark glass base

        // Accent color overlay with very low opacity to add subtle color tint
        int accentAlpha = isHovered ? 0x30 : 0x18; // Very subtle accent tint
        int accentOverlay = (accentAlpha << 24) | (red << 16) | (green << 8) | blue;

        // Draw base glass background
        context.fill(x, y, x + width, y + height, baseGlassColor);

        // Draw subtle accent color overlay
        context.fill(x, y, x + width, y + height, accentOverlay);

        // Glass effect borders
        context.fill(x, y, x + width, y + 1, GLASS_TOP_HIGHLIGHT); // Top highlight
        context.fill(x, y, x + 1, y + height, GLASS_TOP_HIGHLIGHT); // Left highlight
        context.fill(x + width - 1, y, x + width, y + height, GLASS_BOTTOM_SHADOW); // Right shadow
        context.fill(x, y + height - 1, x + width, y + height, GLASS_BOTTOM_SHADOW); // Bottom shadow

        // Draw button text
        int textColor = isHovered ? TEXT_PRIMARY : TEXT_SECONDARY;
        context.drawCenteredTextWithShadow(this.textRenderer, text,
                x + width / 2, y + (height - 8) / 2, textColor);
    }

    /**
     * Draws a modern scrollbar matching MyTownScreen styling
     */
    private void drawModernScrollbar(DrawContext context, int x, int y, int width, int height, int scrollOffset, int maxScroll) {
        // Draw scrollbar track
        context.fill(x, y, x + width, y + height, GLASS_CARD_BG);

        // Calculate scrollbar handle dimensions
        int handleHeight = Math.max(20, height * height / (height + maxScroll));
        float scrollRatio = maxScroll > 0 ? (float)scrollOffset / maxScroll : 0;
        int handleY = y + (int)((height - handleHeight) * scrollRatio);

        // Ensure handle doesn't go out of bounds
        handleY = Math.max(y, Math.min(handleY, y + height - handleHeight));

        // Draw scrollbar handle with glass effect
        context.fill(x, handleY, x + width, handleY + handleHeight, GLASS_CARD_HOVER);
        context.fill(x, handleY, x + width, handleY + 1, GLASS_TOP_HIGHLIGHT); // Top highlight
        context.fill(x, handleY + handleHeight - 1, x + width, handleY + handleHeight, GLASS_BOTTOM_SHADOW); // Bottom shadow
    }

    /**
     * Represents a player entry in the invite list.
     */
    private static class PlayerEntry {
        private final UUID id;
        private final String name;

        public PlayerEntry(UUID id, String name) {
            this.id = id;
            this.name = name;
        }
    }
}
